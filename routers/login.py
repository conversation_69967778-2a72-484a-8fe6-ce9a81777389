"""
Login and Authentication Routes
"""

import os
import jwt
from datetime import datetime, timedelta
from flask import Blueprint, request, render_template, session, redirect, url_for

# Create blueprint
login_bp = Blueprint('login', __name__)

def is_admin_authenticated():
    """Check if admin is authenticated"""
    token = session.get('admin_token')
    if not token:
        return False
    
    try:
        from flask import current_app
        payload = jwt.decode(token, current_app.secret_key, algorithms=['HS256'])
        return payload.get('admin') == True
    except jwt.InvalidTokenError:
        return False

@login_bp.route('/admin')
def admin_login():
    """Admin login page"""
    return render_template('admin_login.html')

@login_bp.route('/admin/login', methods=['POST'])
def admin_login_post():
    """Handle admin login"""
    try:
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()
        
        # Check credentials
        admin_username = os.getenv('ADMIN_USERNAME', 'admin')
        admin_password = os.getenv('ADMIN_PASSWORD', 'admin')
        
        if username == admin_username and password == admin_password:
            # Generate JWT token
            from flask import current_app
            token = jwt.encode(
                {
                    'admin': True,
                    'username': username,
                    'exp': datetime.now() + timedelta(hours=24)
                },
                current_app.secret_key,
                algorithm='HS256'
            )
            
            session['admin_token'] = token
            return redirect(url_for('admin.admin_dashboard'))
        else:
            return render_template('admin_login.html', error='Invalid credentials')
            
    except Exception as e:
        print(f"Admin login error: {e}")
        return render_template('admin_login.html', error='Login failed')

@login_bp.route('/admin/logout')
def admin_logout():
    """Admin logout"""
    session.pop('admin_token', None)
    return redirect(url_for('login.admin_login'))
