"""
Admin Dashboard Routes
"""

from flask import Blueprint, render_template, redirect, url_for, session
from routers.login import is_admin_authenticated

# Create blueprint
admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/admin/dashboard')
def admin_dashboard():
    """Admin dashboard"""
    if not is_admin_authenticated():
        return redirect(url_for('login.admin_login'))
    
    return render_template('admin_dashboard.html')

@admin_bp.route('/admin/logout')
def admin_logout():
    """Admin logout"""
    session.clear()
    return redirect(url_for('login.admin_login'))
