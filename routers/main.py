"""
Main Page Routes
"""

import os
from flask import Blueprint, render_template, redirect, url_for, abort
from services import service_registry

# Create blueprint
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Main page - Anime Waifu Homepage"""
    # Get social media links from environment
    telegram_link = os.getenv('telegram', 'https://t.me/your_channel')
    facebook_link = os.getenv('facebook', 'https://facebook.com/your_page')

    return render_template('index.html',
                         telegram_link=telegram_link,
                         facebook_link=facebook_link)

@main_bp.route('/video/<video_id>')
def video_player(video_id):
    """Video player page"""
    native_banner_html = os.getenv('NATIVE_BANNER_HTML', '')
    return render_template('video_player.html', video_id=video_id, native_banner_html=native_banner_html)

@main_bp.route('/<short_url>')
def short_url_redirect(short_url):
    """Handle short URL redirects like domain.com/abc123"""
    try:
        # Get database service
        db_service = service_registry.get_firebase_service()

        if not db_service:
            abort(404)

        # Check if it's a valid short URL format
        from services.short_url_service import short_url_service as url_service
        if not url_service.is_valid_short_url(short_url):
            abort(404)

        # Try to get video by short URL
        video = None
        if hasattr(db_service, 'get_video_by_short_url'):
            video = db_service.get_video_by_short_url(short_url)

        if video:
            # Redirect to the full video player page
            return redirect(url_for('main.video_player', video_id=video['id']))
        else:
            # Video not found
            abort(404)

    except Exception as e:
        print(f"❌ Error handling short URL {short_url}: {e}")
        abort(404)
