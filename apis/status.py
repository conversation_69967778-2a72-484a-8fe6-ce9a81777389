"""
Status API Endpoints
"""

from flask import Blueprint, jsonify

# Create blueprint
status_api = Blueprint('status_api', __name__)

@status_api.route('/api/status')
def get_status():
    """Get application status"""
    try:
        from services import service_registry

        # Get services from registry
        db_service = service_registry.get_firebase_service()
        r2_service = service_registry.get_r2_service()
        security_service = service_registry.get_security_service()

        status = {
            'application': 'Video Player Platform',
            'status': 'running',
            'version': '3.0.0',
            'database_type': 'MongoDB',
            'services': {
                'mongodb': db_service is not None,
                'r2_storage': r2_service is not None,
                'security': security_service is not None
            }
        }

        # Test MongoDB connection
        if db_service:
            try:
                if hasattr(db_service, 'get_database_status'):
                    db_status = db_service.get_database_status()
                    status['services']['mongodb_connection'] = db_status.get('mongodb_connected', False)
                    status['services']['database_name'] = db_status.get('database_name', 'unknown')
                    status['services']['collections'] = db_status.get('collections', {})
                else:
                    status['services']['mongodb_connection'] = True
                    status['services']['database_name'] = 'unknown'
            except Exception as e:
                status['services']['mongodb_connection'] = False
                status['services']['mongodb_error'] = str(e)

        # Test R2 connection
        if r2_service:
            try:
                # Try to list objects (lightweight operation)
                r2_service.client.list_objects_v2(
                    Bucket=r2_service.bucket_name,
                    MaxKeys=1
                )
                status['services']['r2_connection'] = True
            except Exception as e:
                status['services']['r2_connection'] = False
                status['services']['r2_error'] = str(e)

        return jsonify(status)

    except Exception as e:
        return jsonify({
            'application': 'Video Player Platform',
            'status': 'error',
            'error': str(e)
        }), 500

@status_api.route('/api/health')
def health_check():
    """Simple health check"""
    return jsonify({'status': 'healthy', 'timestamp': '2025-07-14T09:00:00Z'})

@status_api.route('/api/database/status')
def database_status():
    """Get MongoDB database status"""
    try:
        from services import service_registry
        from datetime import datetime

        db_service = service_registry.get_database_service()

        if hasattr(db_service, 'get_database_status'):
            status = db_service.get_database_status()
            return jsonify({
                'database_type': 'MongoDB',
                'database_status': status,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'database_type': 'MongoDB',
                'database_status': 'unknown',
                'message': 'Database service does not support status check',
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        return jsonify({
            'database_type': 'MongoDB',
            'error': str(e),
            'database_status': 'error',
            'timestamp': datetime.now().isoformat()
        }), 500
