"""
Upload API Endpoints
"""

import secrets
import threading
from flask import Blueprint, request, jsonify
from routers.login import is_admin_authenticated
from services import service_registry
from services.upload_progress_service import get_upload_progress_service

# Create blueprint
upload_api = Blueprint('upload_api', __name__)

@upload_api.route('/api/videos/upload', methods=['POST'])
def upload_video():
    """Upload video endpoint (admin only)"""
    # Check admin authentication
    if not is_admin_authenticated():
        return jsonify({'error': 'Unauthorized - Admin access required'}), 401

    try:
        firebase_service = service_registry.get_firebase_service()
        r2_service = service_registry.get_r2_service()

        if not firebase_service or not r2_service:
            return jsonify({'error': 'Service unavailable'}), 503

        if 'video' not in request.files:
            return jsonify({'error': 'No video file provided'}), 400

        video_file = request.files['video']
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()

        if not title:
            return jsonify({'error': 'Title is required'}), 400

        if not video_file.filename:
            return jsonify({'error': 'No file selected'}), 400

        print(f"📤 Starting upload: {video_file.filename} ({title})")

        # Read file data
        file_data = video_file.read()
        file_size = len(file_data)

        print(f"📊 File size: {file_size / (1024*1024):.2f} MB")

        # Upload to R2 (synchronous version)
        r2_key = r2_service.upload_video_sync(
            file_data,
            video_file.filename,
            video_file.content_type or 'video/mp4'
        )

        if not r2_key:
            return jsonify({'error': 'Failed to upload video to storage'}), 500

        print(f"✅ R2 upload successful: {r2_key}")

        # Create video record in Firebase
        video_data = {
            'title': title,
            'description': description,
            'filename': video_file.filename,
            'file_size': file_size,
            'r2_key': r2_key
        }

        video_id = firebase_service.create_video(video_data)

        print(f"✅ Video record created: {video_id}")

        return jsonify({
            'success': True,
            'video_id': video_id,
            'message': 'Video uploaded successfully!'
        })

    except Exception as e:
        print(f"❌ Upload error: {e}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@upload_api.route('/api/videos/upload-with-progress', methods=['POST'])
def upload_video_with_progress():
    """Upload video with real-time progress (admin only)"""
    # Check admin authentication
    if not is_admin_authenticated():
        return jsonify({'error': 'Unauthorized - Admin access required'}), 401

    try:
        progress_service = get_upload_progress_service()
        if not progress_service:
            return jsonify({'error': 'Upload progress service not available'}), 503

        if 'video' not in request.files:
            return jsonify({'error': 'No video file provided'}), 400

        video_file = request.files['video']
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()

        if not title:
            return jsonify({'error': 'Title is required'}), 400

        if not video_file.filename:
            return jsonify({'error': 'No file selected'}), 400

        # Read file data
        file_data = video_file.read()
        file_size = len(file_data)

        print(f"📤 Starting upload with progress: {video_file.filename} ({title})")

        # Create upload session
        session_id = progress_service.create_upload_session(
            video_file.filename,
            file_size,
            title
        )

        # Start upload in background thread
        def upload_worker():
            video_id = progress_service.upload_video_with_progress(
                session_id,
                file_data,
                video_file.filename,
                video_file.content_type or 'video/mp4',
                title,
                description
            )

            if video_id:
                print(f"✅ Upload completed: {video_id}")
            else:
                print(f"❌ Upload failed for session: {session_id}")

        upload_thread = threading.Thread(target=upload_worker)
        upload_thread.daemon = True
        upload_thread.start()

        return jsonify({
            'success': True,
            'session_id': session_id,
            'message': 'Upload started successfully!'
        })

    except Exception as e:
        print(f"❌ Upload error: {e}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@upload_api.route('/api/videos/upload-status/<session_id>')
def get_upload_status(session_id):
    """Get upload status (admin only)"""
    if not is_admin_authenticated():
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        progress_service = get_upload_progress_service()
        if not progress_service:
            return jsonify({'error': 'Upload progress service not available'}), 503

        upload_info = progress_service.get_upload_info(session_id)
        if not upload_info:
            return jsonify({'error': 'Upload session not found'}), 404

        return jsonify(upload_info)

    except Exception as e:
        print(f"❌ Error getting upload status: {e}")
        return jsonify({'error': 'Failed to get upload status'}), 500
