"""
Video API Endpoints
"""

import os
import secrets
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, redirect
from routers.login import is_admin_authenticated
from services import service_registry

# Create blueprint
videos_api = Blueprint('videos_api', __name__)

@videos_api.route('/api/videos/test')
def test_endpoint():
    """Test endpoint to verify API is working"""
    return jsonify({
        'status': 'working',
        'message': 'API is responding correctly',
        'timestamp': datetime.now().isoformat()
    })

@videos_api.route('/api/videos/recent')
def get_recent_videos():
    """Get recent videos from Firebase"""
    try:
        firebase_service = service_registry.get_firebase_service()

        if firebase_service:
            print("🔍 Attempting to get recent videos...")
            # Get real videos from Firebase with timeout protection
            videos = firebase_service.get_recent_videos(limit=10)
            print(f"📊 Returning {len(videos)} videos")
            return jsonify(videos)
        else:
            print("⚠️ Firebase service not available")
            return jsonify([])
    except Exception as e:
        print(f"❌ Error getting recent videos: {e}")
        return jsonify([])

@videos_api.route('/api/videos/<video_id>')
def get_video_info(video_id):
    """Get video information from database"""
    try:
        db_service = service_registry.get_firebase_service()

        if db_service:
            video = db_service.get_video(video_id)
            if video:
                # Convert datetime objects to ISO strings (handle both datetime objects and strings)
                if 'created_at' in video and video['created_at']:
                    if hasattr(video['created_at'], 'isoformat'):
                        video['created_at'] = video['created_at'].isoformat()
                    # If it's already a string, keep it as is

                if 'expires_at' in video and video['expires_at']:
                    if hasattr(video['expires_at'], 'isoformat'):
                        video['expires_at'] = video['expires_at'].isoformat()
                    # If it's already a string, keep it as is

                # Remove sensitive data
                video.pop('r2_key', None)
                video.pop('analytics', None)
                return jsonify(video)
            else:
                return jsonify({'error': 'Video not found'}), 404
        else:
            return jsonify({'error': 'Service unavailable'}), 503
    except Exception as e:
        print(f"Error getting video info: {e}")
        return jsonify({'error': 'Video not found'}), 404

@videos_api.route('/api/videos/short/<short_url>')
def get_video_by_short_url(short_url):
    """Get video information by short URL"""
    try:
        db_service = service_registry.get_firebase_service()

        if db_service and hasattr(db_service, 'get_video_by_short_url'):
            video = db_service.get_video_by_short_url(short_url)
            if video:
                # Convert datetime objects to ISO strings (handle both datetime objects and strings)
                if 'created_at' in video and video['created_at']:
                    if hasattr(video['created_at'], 'isoformat'):
                        video['created_at'] = video['created_at'].isoformat()
                    # If it's already a string, keep it as is

                if 'expires_at' in video and video['expires_at']:
                    if hasattr(video['expires_at'], 'isoformat'):
                        video['expires_at'] = video['expires_at'].isoformat()
                    # If it's already a string, keep it as is

                # Remove sensitive data
                video.pop('r2_key', None)
                video.pop('analytics', None)
                return jsonify(video)
            else:
                return jsonify({'error': 'Video not found'}), 404
        else:
            return jsonify({'error': 'Service unavailable'}), 503
    except Exception as e:
        print(f"Error getting video by short URL: {e}")
        return jsonify({'error': 'Video not found'}), 404

@videos_api.route('/api/videos/<video_id>/stream')
def get_video_stream(video_id):
    """Get video stream URL with session management"""
    try:
        from app import firebase_service, r2_service, run_async

        if not firebase_service or not r2_service:
            return jsonify({'error': 'Service unavailable'}), 503

        # Get video info
        video = run_async(firebase_service.get_video(video_id))
        if not video:
            return jsonify({'error': 'Video not found'}), 404

        # Check if video is expired
        if video.get('expires_at') and video['expires_at'] < datetime.now():
            return jsonify({'error': 'Video has expired'}), 410

        # Get client info
        ip_address = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')

        # Create session
        session_id = run_async(firebase_service.create_session(video_id, ip_address, user_agent))

        # Check view session (120min limit)
        should_count_view = run_async(firebase_service.check_view_session(video_id, ip_address))

        if should_count_view:
            # Update view count
            run_async(firebase_service.update_video_views(
                video_id,
                ip_address,
                user_agent,
                request.headers.get('Referer', '')
            ))

        # Get stream URL from R2
        stream_url = run_async(r2_service.get_video_stream_url(video['r2_key'], session_id))

        if not stream_url:
            return jsonify({'error': 'Failed to generate stream URL'}), 500

        return jsonify({
            'stream_url': stream_url,
            'session_id': session_id,
            'view_counted': should_count_view
        })

    except Exception as e:
        print(f"Error getting video stream: {e}")
        return jsonify({'error': 'Failed to get video stream'}), 500

@videos_api.route('/api/videos/<video_id>/redirect')
def video_redirect(video_id):
    """Handle video redirect"""
    try:
        redirect_url = os.getenv('REDIRECT_LINK', 'https://example.com')
        return redirect(redirect_url)
    except Exception as e:
        print(f"Error handling redirect: {e}")
        return jsonify({'error': 'Redirect failed'}), 500
