"""
Admin API Endpoints
"""

from datetime import datetime, timedelta
from flask import Blueprint, jsonify
from routers.login import is_admin_authenticated
from services import service_registry

# Create blueprint
admin_api = Blueprint('admin_api', __name__)

@admin_api.route('/api/admin/analytics')
def get_admin_analytics():
    """Get analytics data for admin dashboard"""
    if not is_admin_authenticated():
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        firebase_service = service_registry.get_firebase_service()
        r2_service = service_registry.get_r2_service()

        if not firebase_service or not r2_service:
            return jsonify({'error': 'Service unavailable'}), 503

        # Get real analytics data (synchronous)
        analytics = firebase_service.get_analytics_data_sync()

        # Get real storage stats (synchronous)
        storage_stats = r2_service.get_storage_stats_sync()

        return jsonify({
            'analytics': analytics,
            'storage': storage_stats
        })

    except Exception as e:
        print(f"Error getting admin analytics: {e}")
        return jsonify({'error': 'Failed to get analytics'}), 500

@admin_api.route('/api/admin/videos')
def get_admin_videos():
    """Get all videos for admin management"""
    if not is_admin_authenticated():
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        firebase_service = service_registry.get_firebase_service()

        if firebase_service:
            # Get real videos from Firebase (admin version with all data)
            videos = firebase_service.get_admin_videos(limit=100)
            return jsonify(videos)
        else:
            return jsonify([])
    except Exception as e:
        print(f"Error getting admin videos: {e}")
        return jsonify([])

@admin_api.route('/api/admin/videos/<video_id>', methods=['DELETE'])
def delete_video_admin(video_id):
    """Delete video (admin only)"""
    if not is_admin_authenticated():
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        from app import firebase_service, r2_service, run_async

        if not firebase_service or not r2_service:
            return jsonify({'error': 'Service unavailable'}), 503

        # Get video info first
        video = run_async(firebase_service.get_video(video_id))
        if not video:
            return jsonify({'error': 'Video not found'}), 404

        # Delete from R2 storage
        if video.get('r2_key'):
            success = run_async(r2_service.delete_video(video['r2_key']))
            if not success:
                print(f"Warning: Failed to delete video from R2 storage: {video['r2_key']}")

        # Delete from Firebase
        success = run_async(firebase_service.delete_video(video_id))
        if success:
            return jsonify({
                'success': True,
                'message': 'Video deleted successfully'
            })
        else:
            return jsonify({'error': 'Failed to delete video from database'}), 500

    except Exception as e:
        print(f"Error deleting video: {e}")
        return jsonify({'error': 'Failed to delete video'}), 500
