#!/usr/bin/env python3
"""
Flask Video Player Application
Minimal, fast video streaming platform with modular structure
"""

import os
import secrets
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from flask import Flask
from flask_cors import CORS
from flask_socketio import SocketIO
import boto3
from botocore.config import Config
from cryptography.fernet import Fernet

# Import services
from services.firebase_realtime_service import FirebaseRealtimeService
from services.r2_service import R2Service
from services.security_service import SecurityService
from services import service_registry

# Import blueprints
from routers.main import main_bp
from routers.login import login_bp
from routers.admin import admin_bp
from routers.register import register_bp
from apis.videos import videos_api
from apis.upload import upload_api
from apis.admin import admin_api
from apis.status import status_api

# Global services
firebase_service = None
r2_client = None
cipher_suite = None
r2_service = None
security_service = None
executor = ThreadPoolExecutor(max_workers=10)

def init_services():
    """Initialize Firebase Realtime Database and R2 services"""
    global firebase_service, r2_client, cipher_suite, r2_service, security_service

    # Initialize Firebase Realtime Database Service
    try:
        service_account_path = os.getenv('FIREBASE_SERVICE_ACCOUNT')
        database_url = os.getenv('FIREBASE_DATABASE_URL')
        print("🔥 Initializing Firebase Realtime Database service...")
        firebase_service = FirebaseRealtimeService(service_account_path, database_url)
        service_registry.set_firebase_service(firebase_service)
        print("✅ Firebase Realtime Database service initialized successfully")

    except Exception as e:
        print(f"❌ Firebase initialization failed: {e}")
        return False

    # Initialize R2 client
    try:
        r2_client = boto3.client(
            's3',
            endpoint_url=os.getenv('R2_ENDPOINT_URL'),
            aws_access_key_id=os.getenv('R2_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('R2_SECRET_ACCESS_KEY'),
            config=Config(signature_version='s3v4'),
            region_name='auto'
        )
        r2_service = R2Service(r2_client)
        service_registry.set_r2_service(r2_service)
        print("✅ R2 storage initialized successfully")
    except Exception as e:
        print(f"❌ R2 initialization failed: {e}")
        return False

    # Initialize encryption
    encryption_key = os.getenv('VIDEO_ENCRYPTION_KEY')
    if encryption_key:
        cipher_suite = Fernet(encryption_key.encode())
        print("✅ Video encryption initialized")

    # Initialize security service
    security_service = SecurityService(cipher_suite)
    service_registry.set_security_service(security_service)
    print("✅ Security service initialized")

    return True

def create_app():
    """Create and configure Flask application"""
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = os.getenv('JWT_SECRET', secrets.token_hex(32))
    app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB

    # Enable CORS
    CORS(app)

    # Register blueprints
    app.register_blueprint(main_bp)
    app.register_blueprint(login_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(register_bp)
    app.register_blueprint(videos_api)
    app.register_blueprint(upload_api)
    app.register_blueprint(admin_api)
    app.register_blueprint(status_api)

    return app

# Create app instance
app = create_app()

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# Initialize upload progress service
from services.upload_progress_service import init_upload_progress_service
upload_progress_service = init_upload_progress_service(socketio)

# Initialize services once at startup
print("🔧 Initializing services...")
success = init_services()
if not success:
    print("⚠️ Some services failed to initialize. Check your configuration.")

# Helper function to run async operations
def run_async(coro):
    """Run async function in thread pool"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()



if __name__ == '__main__':
    # Get configuration from environment
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'

    print("🎬 Starting Flask Video Player Application...")
    print("📝 Firebase Realtime Database & R2 integration with modular structure")
    print("🔧 Using Firebase Realtime Database as primary database")
    print(f"🌐 Access: http://localhost:{port}")
    print(f"👨‍💼 Admin: http://localhost:{port}/admin")
    print("")
    print("📁 Project Structure:")
    print("   ├── routers/     - Page routes (login, admin, main, register)")
    print("   ├── apis/        - API endpoints (videos, upload, admin)")
    print("   ├── services/    - Business logic services")
    print("   └── templates/   - HTML templates")
    print("")
    print("🔗 Social Media Links:")
    print(f"   📱 Telegram: {os.getenv('telegram', 'Not configured')}")
    print(f"   📘 Facebook: {os.getenv('facebook', 'Not configured')}")
    print("")

    socketio.run(app, host=host, port=port, debug=debug)
