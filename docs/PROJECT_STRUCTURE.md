# Flask Video Player - Modular Project Structure

## 📁 Project Organization

```
player/
├── app.py                      # Main Flask application entry point
├── requirements.txt            # Python dependencies
├── .env.example               # Environment configuration template
├── README.md                  # Project documentation
├── PROJECT_STRUCTURE.md       # This file
├── install.sh                 # Installation script
├── run.py                     # Alternative runner script
│
├── routers/                   # Page Routes (Blueprint-based)
│   ├── __init__.py
│   ├── main.py               # Homepage and video player routes
│   ├── login.py              # Authentication routes
│   ├── admin.py              # Admin dashboard routes
│   └── register.py           # User registration routes (future)
│
├── apis/                      # API Endpoints (Blueprint-based)
│   ├── __init__.py
│   ├── videos.py             # Video-related API endpoints
│   ├── upload.py             # Video upload API (admin-only)
│   └── admin.py              # Admin management APIs
│
├── services/                  # Business Logic Services
│   ├── __init__.py
│   ├── firebase_service.py   # Firebase/Firestore operations
│   ├── r2_service.py         # Cloudflare R2 storage operations
│   └── security_service.py   # Security and anti-scraping features
│
├── templates/                 # HTML Templates
│   ├── base.html             # Base template with common styles
│   ├── index.html            # Anime waifu homepage
│   ├── video_player.html     # Video player page
│   ├── admin_login.html      # Admin login form
│   └── admin_dashboard.html  # Admin dashboard
│
├── config/                    # Configuration Files
│   ├── firebase-service-account.json
│   └── firebase-service-account.example.json
│
└── tests/                     # Test Files
    ├── test_app.py
    └── simple_app.py         # Previous monolithic version
```

## 🔗 Route Structure

### Page Routes (routers/)

#### main.py
- `GET /` - Anime waifu homepage
- `GET /video/<video_id>` - Video player page

#### login.py
- `GET /admin` - Admin login page
- `POST /admin/login` - Handle admin authentication
- `GET /admin/logout` - Admin logout

#### admin.py
- `GET /admin/dashboard` - Admin dashboard (requires auth)

#### register.py
- `GET /register` - User registration (future implementation)
- `POST /register` - Handle user registration (future implementation)

### API Routes (apis/)

#### videos.py
- `GET /api/videos/recent` - Get recent videos list
- `GET /api/videos/<video_id>` - Get video information
- `GET /api/videos/<video_id>/stream` - Get video stream URL
- `GET /api/videos/<video_id>/redirect` - Handle monetization redirect

#### upload.py
- `POST /api/videos/upload` - Upload video (admin-only)

#### admin.py
- `GET /api/admin/analytics` - Get analytics data (admin-only)
- `GET /api/admin/videos` - Get all videos for management (admin-only)
- `DELETE /api/admin/videos/<video_id>` - Delete video (admin-only)

## 🎯 Key Features

### ✅ Modular Architecture
- **Separation of Concerns**: Routes, APIs, and business logic are separated
- **Blueprint-based**: Each module is a Flask Blueprint for easy management
- **Scalable**: Easy to add new features without cluttering main app

### ✅ Security Features
- **Admin-only Upload**: Only authenticated admins can upload videos
- **JWT Authentication**: Secure token-based authentication
- **Session Management**: Proper session handling and logout

### ✅ Anime Waifu Design
- **Responsive Design**: Works on desktop and mobile
- **Social Media Integration**: Telegram and Facebook buttons
- **Magical Effects**: Sparkle animations and gradient backgrounds
- **Character Theme**: Sakura-chan anime waifu character

### ✅ API Organization
- **RESTful Design**: Clean API endpoints following REST principles
- **Error Handling**: Proper error responses and status codes
- **Data Validation**: Input validation and sanitization

## 🚀 Running the Application

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python app.py

# Access URLs
# Homepage: http://localhost:5001
# Admin: http://localhost:5001/admin
```

## 🔧 Adding New Features

### Adding a New Route
1. Create a new file in `routers/` (e.g., `routers/profile.py`)
2. Create a Blueprint and define routes
3. Import and register the Blueprint in `app.py`

### Adding a New API
1. Create a new file in `apis/` (e.g., `apis/comments.py`)
2. Create a Blueprint and define API endpoints
3. Import and register the Blueprint in `app.py`

### Adding Business Logic
1. Create a new service in `services/` (e.g., `services/email_service.py`)
2. Import and use the service in your routes or APIs

## 📝 Development Notes

- **Debug Mode**: Enabled by default for development
- **Hot Reload**: Flask automatically reloads on file changes
- **Error Handling**: Comprehensive error handling throughout the application
- **Logging**: Console logging for debugging and monitoring

## 🎨 Frontend Features

- **Minimal Code**: Clean HTML/CSS/JS with minimal dependencies
- **Responsive**: Mobile-first responsive design
- **Animations**: CSS animations and JavaScript effects
- **Modern UI**: Glass-morphism and gradient designs
- **Fast Loading**: Optimized for performance and low CPU usage
