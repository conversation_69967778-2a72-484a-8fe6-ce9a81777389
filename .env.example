# Admin credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here

# JWT Secret
JWT_SECRET=your_jwt_secret_key_here

# R2 Configuration
R2_ENDPOINT_URL=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your_r2_access_key
R2_SECRET_ACCESS_KEY=your_r2_secret_key
R2_BUCKET_NAME=your_bucket_name
R2_CUSTOM_DOMAIN=https://your-custom-domain.com

# Video Streaming Configuration
# USE_DIRECT_STREAMING=true (uses signed URLs - reduces server bandwidth costs)
# USE_DIRECT_STREAMING=false (streams through server - more secure but higher costs)
USE_DIRECT_STREAMING=true

# Video Security Configuration
# Generate with: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
VIDEO_ENCRYPTION_KEY=your_base64_encryption_key_here

# Firebase Configuration
# Option 1: Path to service account JSON file
FIREBASE_SERVICE_ACCOUNT=firebase-service-account.json

# Option 2: Service account JSON as environment variable (for production)
# FIREBASE_CONFIG={"type":"service_account","project_id":"your-project",...}

# Monetization Configuration
REDIRECT_LINK=https://www.profitableratecpm.com/dr3iiguyd?key=4acaaf8e68d93c7323444154bdb2df08
NATIVE_BANNER_HTML=<div style="text-align:center;padding:20px;background:#f5f5f5;border-radius:8px;margin:10px 0;color:#333;"><h3>Your Banner Title</h3><p>Your banner content here</p></div>

