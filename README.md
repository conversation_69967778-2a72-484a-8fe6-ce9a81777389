# Video Player - Fast Async Flask Application

A minimal, fast, and secure video streaming platform built with Flask (Quart) async support, Firebase Firestore, and Cloudflare R2 storage.

## Features

- **Async Flask (Quart)** - High-performance async web framework
- **Firebase Firestore** - Real-time database for video metadata and analytics
- **Cloudflare R2** - Cost-optimized video storage with custom domain support
- **Session-based Security** - 1 redirect per 20-min session, 1 view count per 120-min session
- **Anti-scraping Protection** - Rate limiting, bot detection, security headers
- **Admin Dashboard** - Real-time analytics and video management
- **Minimal Frontend** - Clean HTML/CSS/JS with fullscreen support, no download button
- **Native Banner Support** - Customizable banner display below video player

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository>
   cd player
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Setup Firebase**
   - Create a Firebase project
   - Download service account JSON
   - Place it at `config/firebase-service-account.json`

4. **Setup Cloudflare R2**
   - Create R2 bucket
   - Get API credentials
   - Configure custom domain (optional but recommended for cost optimization)

5. **Run Application**
   ```bash
   python run.py
   ```

## Configuration

### Environment Variables

```bash
# Admin credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password

# JWT Secret
JWT_SECRET=your_jwt_secret_key

# R2 Configuration
R2_ENDPOINT_URL=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your_r2_access_key
R2_SECRET_ACCESS_KEY=your_r2_secret_key
R2_BUCKET_NAME=your_bucket_name
R2_CUSTOM_DOMAIN=https://your-custom-domain.com

# Video Streaming (true = direct URLs, false = proxy through server)
USE_DIRECT_STREAMING=true

# Video Security
VIDEO_ENCRYPTION_KEY=your_base64_encryption_key

# Firebase
FIREBASE_SERVICE_ACCOUNT=config/firebase-service-account.json

# Monetization
REDIRECT_LINK=https://your-redirect-link.com
NATIVE_BANNER_HTML=<div>Your banner HTML</div>
```

### Generate Encryption Key

```bash
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
```

## API Endpoints

### Public Endpoints
- `GET /` - Home page with upload form
- `GET /video/<video_id>` - Video player page
- `POST /api/videos/upload` - Upload video
- `GET /api/videos/recent` - Get recent videos
- `GET /api/videos/<video_id>` - Get video info
- `GET /api/videos/<video_id>/stream` - Get video stream URL
- `GET /api/videos/<video_id>/redirect` - Handle monetization redirect

### Admin Endpoints
- `GET /admin` - Admin login
- `POST /admin/login` - Admin authentication
- `GET /admin/dashboard` - Admin dashboard
- `GET /api/admin/analytics` - Analytics data
- `GET /api/admin/videos` - All videos (admin)
- `DELETE /api/admin/videos/<video_id>` - Delete video

## Security Features

- **Rate Limiting** - Per-IP rate limits for all endpoints
- **Bot Detection** - Automatic detection of automated requests
- **IP Blocking** - Automatic blocking of suspicious IPs
- **Security Headers** - Anti-scraping HTTP headers
- **Session Management** - Secure session-based access control
- **Video Encryption** - Optional video URL encryption
- **HMAC Signatures** - Signed video access tokens

## Performance Optimizations

- **Async Operations** - All database and storage operations are async
- **Direct Streaming** - Videos served directly from R2 with custom domain (free egress)
- **Minimal Dependencies** - Only essential packages for fast startup
- **Efficient Caching** - Smart caching strategies for metadata
- **Connection Pooling** - Optimized database connections

## Cost Optimization

- **R2 Custom Domain** - Free egress when using custom domain
- **Direct Streaming** - Reduces server bandwidth costs
- **Session Limits** - Prevents abuse and reduces costs
- **Efficient Storage** - Automatic cleanup of expired videos

## Development

### Project Structure
```
player/
├── app.py                 # Main Flask application
├── run.py                 # Application runner
├── requirements.txt       # Python dependencies
├── config/               # Configuration files
│   └── firebase-service-account.json
├── services/             # Service modules
│   ├── firebase_service.py
│   ├── r2_service.py
│   └── security_service.py
└── templates/            # HTML templates
    ├── base.html
    ├── index.html
    ├── video_player.html
    ├── admin_login.html
    └── admin_dashboard.html
```

### Running in Production

```bash
# Set production environment
export DEBUG=false

# Run with Hypercorn
python run.py
```

## License

MIT License - see LICENSE file for details.
