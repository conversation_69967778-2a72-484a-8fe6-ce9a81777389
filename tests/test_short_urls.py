#!/usr/bin/env python3
"""
Test Short URL Functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.short_url_service import short_url_service

def test_short_url_generation():
    """Test short URL generation"""
    print("🔗 Testing Short URL Generation")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {"video_id": "123e4567-e89b-12d3-a456-426614174000", "title": "Anime Episode 1"},
        {"video_id": "987fcdeb-51a2-43d1-9c4f-123456789abc", "title": "Sakura Collection"},
        {"video_id": "456789ab-cdef-1234-5678-9abcdef01234", "title": "Test Video"},
        {"video_id": "abcdef12-3456-7890-abcd-ef1234567890", "title": ""},
        {"video_id": "11111111-2222-3333-4444-555555555555", "title": "Very Long Title That Should Be Truncated"},
    ]
    
    existing_urls = set()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}:")
        print(f"   Video ID: {test_case['video_id']}")
        print(f"   Title: '{test_case['title']}'")
        
        # Generate short URL
        short_url = short_url_service.generate_unique_short_url(
            test_case['video_id'], 
            test_case['title'], 
            existing_urls
        )
        
        print(f"   Short URL: {short_url}")
        print(f"   Length: {len(short_url)}")
        print(f"   Valid: {short_url_service.is_valid_short_url(short_url)}")
        
        # Add to existing URLs to test uniqueness
        existing_urls.add(short_url)
        
        # Test different generation methods
        readable = short_url_service.generate_readable_short_url(test_case['title'])
        hash_based = short_url_service.generate_short_url(test_case['video_id'], test_case['title'])
        random_url = short_url_service.generate_random_short_url()
        
        print(f"   Readable: {readable}")
        print(f"   Hash-based: {hash_based}")
        print(f"   Random: {random_url}")
    
    print(f"\n✅ Generated {len(existing_urls)} unique short URLs")
    print(f"📊 All URLs: {sorted(existing_urls)}")

def test_url_validation():
    """Test URL validation"""
    print("\n🔍 Testing URL Validation")
    print("=" * 30)
    
    valid_urls = ["abc123", "XyZ789", "a1b2c3", "ABCDEF"]
    invalid_urls = ["abc12", "abc1234", "abc@123", "ab c123", ""]
    
    print("✅ Valid URLs:")
    for url in valid_urls:
        is_valid = short_url_service.is_valid_short_url(url)
        print(f"   {url} -> {is_valid}")
    
    print("\n❌ Invalid URLs:")
    for url in invalid_urls:
        is_valid = short_url_service.is_valid_short_url(url)
        print(f"   '{url}' -> {is_valid}")

def test_collision_handling():
    """Test collision handling"""
    print("\n🔄 Testing Collision Handling")
    print("=" * 35)
    
    # Simulate existing URLs
    existing_urls = {"abc123", "def456", "ghi789"}
    
    # Try to generate URLs that might collide
    video_id = "test-video-id"
    title = "Test Title"
    
    print(f"Existing URLs: {existing_urls}")
    
    for i in range(5):
        short_url = short_url_service.generate_unique_short_url(video_id, title, existing_urls)
        print(f"Generated: {short_url} (unique: {short_url not in existing_urls})")
        existing_urls.add(short_url)

if __name__ == "__main__":
    test_short_url_generation()
    test_url_validation()
    test_collision_handling()
    
    print("\n🎉 Short URL testing complete!")
    print("\n💡 Example usage:")
    print("   Original: https://domain.com/video/123e4567-e89b-12d3-a456-426614174000")
    print("   Short:    https://domain.com/abc123")
