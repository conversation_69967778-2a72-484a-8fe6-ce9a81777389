#!/usr/bin/env python3
"""
MongoDB Connection Test Script
"""

import os
from dotenv import load_dotenv
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# Load environment variables
load_dotenv()

def test_mongodb_connection():
    """Test MongoDB connection with different configurations"""
    
    connection_string = os.getenv('mongo_db')
    if not connection_string:
        print("❌ No MongoDB connection string found in .env file")
        return False
    
    print(f"🔗 Testing MongoDB connection...")
    print(f"📝 Connection string: {connection_string[:50]}...")
    
    # Test configurations
    configs = [
        {
            'name': 'Standard SSL',
            'options': {
                'serverSelectionTimeoutMS': 5000,
                'connectTimeoutMS': 5000,
                'socketTimeoutMS': 5000,
            }
        },
        {
            'name': 'TLS with invalid certs allowed',
            'options': {
                'serverSelectionTimeoutMS': 5000,
                'connectTimeoutMS': 5000,
                'socketTimeoutMS': 5000,
                'tls': True,
                'tlsAllowInvalidCertificates': True,
            }
        },
        {
            'name': 'No SSL/TLS',
            'options': {
                'serverSelectionTimeoutMS': 5000,
                'connectTimeoutMS': 5000,
                'socketTimeoutMS': 5000,
                'tls': False,
            }
        }
    ]
    
    for config in configs:
        print(f"\n🧪 Testing: {config['name']}")
        try:
            client = MongoClient(connection_string, **config['options'])
            
            # Test connection
            client.admin.command('ping')
            
            # Get database info
            db_name = 'video_platform'
            db = client[db_name]
            
            print(f"✅ Connection successful!")
            print(f"📊 Database: {db_name}")
            print(f"📁 Collections: {db.list_collection_names()}")
            
            # Test a simple operation
            test_collection = db.test_connection
            result = test_collection.insert_one({'test': True, 'timestamp': '2025-07-14'})
            print(f"✅ Test write successful: {result.inserted_id}")
            
            # Clean up test document
            test_collection.delete_one({'_id': result.inserted_id})
            print(f"✅ Test cleanup successful")
            
            client.close()
            return True
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            print(f"❌ Connection failed: {e}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n💡 All connection attempts failed. Possible solutions:")
    print(f"   1. Check your internet connection")
    print(f"   2. Verify MongoDB Atlas cluster is running")
    print(f"   3. Check IP whitelist in MongoDB Atlas")
    print(f"   4. Verify username/password in connection string")
    print(f"   5. Try connecting from MongoDB Compass first")
    
    return False

def get_mongodb_info():
    """Get MongoDB connection information"""
    connection_string = os.getenv('mongo_db')
    if not connection_string:
        return
    
    print(f"\n📋 MongoDB Configuration:")
    print(f"   Connection String: {connection_string}")
    
    # Parse connection string
    if '@' in connection_string:
        parts = connection_string.split('@')
        if len(parts) >= 2:
            cluster_info = parts[1].split('/')[0]
            print(f"   Cluster: {cluster_info}")
    
    if '/' in connection_string:
        db_name = connection_string.split('/')[-1].split('?')[0]
        if db_name:
            print(f"   Database: {db_name}")

if __name__ == "__main__":
    print("🍃 MongoDB Connection Test")
    print("=" * 50)
    
    get_mongodb_info()
    
    success = test_mongodb_connection()
    
    if success:
        print(f"\n🎉 MongoDB connection is working!")
        print(f"💡 You can now restart the application to use MongoDB")
    else:
        print(f"\n⚠️  MongoDB connection failed")
        print(f"💡 The application will use local SQLite database instead")
        print(f"🔧 This is perfectly fine for development and testing!")
