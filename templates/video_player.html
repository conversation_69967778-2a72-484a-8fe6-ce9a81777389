{% extends "base.html" %}

{% block title %}Video Player{% endblock %}

{% block content %}
<div class="video-container">
    <div class="video-header">
        <a href="/" style="color: #007bff; text-decoration: none;">← Back to Home</a>
    </div>
    
    <div id="videoInfo" class="video-info">
        <div class="loading">
            <div class="spinner"></div>
            <p>Loading video...</p>
        </div>
    </div>
    
    <div id="videoPlayer" class="video-player-container">
        <!-- Video player will be loaded here -->
    </div>
    
    <div id="nativeBanner" class="native-banner">
        <!-- Native banner will be loaded here -->
    </div>
    
    <div class="redirect-section">
        <button id="redirectBtn" class="btn" style="display: none;">Continue to Content</button>
        <p id="redirectInfo" style="display: none; color: #888; font-size: 14px;">
            Click the button above to continue. You can only use this redirect once per session.
        </p>
    </div>
</div>

<style>
.video-container {
    max-width: 1000px;
    margin: 0 auto;
}

.video-header {
    padding: 20px 0;
    border-bottom: 1px solid #333;
    margin-bottom: 30px;
}

.video-info {
    margin-bottom: 30px;
}

.video-info h1 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 2em;
}

.video-info p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 20px;
}

.video-meta {
    display: flex;
    gap: 30px;
    font-size: 14px;
    color: #888;
    margin-bottom: 20px;
}

.share-section {
    margin-top: 20px;
    padding: 15px;
    background: #2a2a2a;
    border-radius: 8px;
    border: 1px solid #444;
}

.share-section label {
    display: block;
    margin-bottom: 8px;
    color: #fff;
    font-weight: 500;
}

.share-url {
    display: flex;
    gap: 10px;
    align-items: center;
}

.share-url input {
    flex: 1;
    padding: 8px 12px;
    background: #1a1a1a;
    border: 1px solid #555;
    border-radius: 4px;
    color: #fff;
    font-family: monospace;
    font-size: 14px;
}

.share-url input:focus {
    outline: none;
    border-color: #007bff;
}

.copy-btn {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.copy-btn:hover {
    background: #0056b3;
}

.video-player-container {
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 30px;
    position: relative;
}

.video-player {
    width: 100%;
    height: auto;
    min-height: 400px;
    background: #000;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.play-btn {
    background: #007bff;
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: #007bff;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s;
}

.time-display {
    color: white;
    font-size: 14px;
    min-width: 100px;
}

.fullscreen-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
}

.native-banner {
    margin: 30px 0;
    text-align: center;
}

.redirect-section {
    text-align: center;
    padding: 30px;
    background: #1a1a1a;
    border-radius: 10px;
    border: 1px solid #333;
}

.error-message {
    background: #721c24;
    border: 1px solid #dc3545;
    color: #f8d7da;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    margin: 20px 0;
}

@media (max-width: 768px) {
    .video-meta {
        flex-direction: column;
        gap: 10px;
    }
    
    .video-controls {
        padding: 15px;
        gap: 10px;
    }
    
    .time-display {
        min-width: 80px;
        font-size: 12px;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
const videoId = '{{ video_id }}';
let sessionId = null;
let streamUrl = null;

document.addEventListener('DOMContentLoaded', function() {
    loadVideoInfo();
    loadNativeBanner();
});

async function loadVideoInfo() {
    try {
        const response = await fetch(`/api/videos/${videoId}`);
        const video = await response.json();
        
        if (response.ok) {
            displayVideoInfo(video);
            await loadVideoPlayer();
        } else {
            showVideoError(video.error || 'Video not found');
        }
    } catch (error) {
        console.error('Error loading video info:', error);
        showVideoError('Failed to load video');
    }
}

function displayVideoInfo(video) {
    const videoInfo = document.getElementById('videoInfo');

    // Generate short URL for sharing
    const shortUrl = video.short_url ? `${window.location.origin}/${video.short_url}` : '';

    videoInfo.innerHTML = `
        <h1>${video.title}</h1>
        <p>${video.description || 'No description available'}</p>
        <div class="video-meta">
            <span>Views: ${video.views || 0}</span>
            <span>Uploaded: ${new Date(video.created_at).toLocaleDateString()}</span>
            <span>Size: ${(video.file_size / (1024*1024)).toFixed(2)} MB</span>
        </div>
        ${shortUrl ? `
        <div class="share-section">
            <label>Share this video:</label>
            <div class="share-url">
                <input type="text" value="${shortUrl}" readonly onclick="this.select()" />
                <button onclick="copyToClipboard('${shortUrl}')" class="copy-btn">Copy</button>
            </div>
        </div>
        ` : ''}
    `;
}

async function loadVideoPlayer() {
    try {
        const response = await fetch(`/api/videos/${videoId}/stream`);
        const streamData = await response.json();
        
        if (response.ok) {
            sessionId = streamData.session_id;
            streamUrl = streamData.stream_url;
            
            displayVideoPlayer(streamUrl);
            setupRedirectButton();
        } else {
            showVideoError(streamData.error || 'Failed to load video stream');
        }
    } catch (error) {
        console.error('Error loading video player:', error);
        showVideoError('Failed to load video stream');
    }
}

function displayVideoPlayer(url) {
    const videoPlayer = document.getElementById('videoPlayer');
    
    videoPlayer.innerHTML = `
        <video class="video-player" controls controlsList="nodownload">
            <source src="${url}" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    `;
    
    const video = videoPlayer.querySelector('video');
    
    // Enable fullscreen
    video.addEventListener('dblclick', function() {
        if (video.requestFullscreen) {
            video.requestFullscreen();
        } else if (video.webkitRequestFullscreen) {
            video.webkitRequestFullscreen();
        } else if (video.mozRequestFullScreen) {
            video.mozRequestFullScreen();
        }
    });
    
    // Disable right-click context menu
    video.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
    
    // Disable download attribute
    video.removeAttribute('download');
}

function setupRedirectButton() {
    const redirectBtn = document.getElementById('redirectBtn');
    const redirectInfo = document.getElementById('redirectInfo');
    
    redirectBtn.style.display = 'inline-block';
    redirectInfo.style.display = 'block';
    
    redirectBtn.addEventListener('click', async function() {
        if (!sessionId) {
            showError('Session expired. Please refresh the page.');
            return;
        }
        
        try {
            const response = await fetch(`/api/videos/${videoId}/redirect?session=${sessionId}`);
            
            if (response.redirected) {
                window.open(response.url, '_blank');
                redirectBtn.disabled = true;
                redirectBtn.textContent = 'Redirect Used';
                redirectInfo.textContent = 'You have used your redirect for this session.';
            } else {
                const result = await response.json();
                showError(result.error || 'Redirect failed');
                
                if (response.status === 429) {
                    redirectBtn.disabled = true;
                    redirectBtn.textContent = 'Redirect Used';
                    redirectInfo.textContent = 'You have used your redirect for this session.';
                }
            }
        } catch (error) {
            console.error('Redirect error:', error);
            showError('Redirect failed');
        }
    });
}

function loadNativeBanner() {
    const nativeBanner = document.getElementById('nativeBanner');
    
    // Load native banner HTML from environment variable
    const bannerHtml = `{{ native_banner_html | safe }}` || '';
    
    if (bannerHtml.trim()) {
        nativeBanner.innerHTML = bannerHtml;
    }
}

function showVideoError(message) {
    const videoInfo = document.getElementById('videoInfo');
    const videoPlayer = document.getElementById('videoPlayer');
    
    videoInfo.innerHTML = `
        <div class="error-message">
            <h2>Error</h2>
            <p>${message}</p>
        </div>
    `;
    
    videoPlayer.innerHTML = '';
    
    document.getElementById('redirectBtn').style.display = 'none';
    document.getElementById('redirectInfo').style.display = 'none';
}

function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const copyBtn = event.target;
        const originalText = copyBtn.textContent;
        copyBtn.textContent = 'Copied!';
        copyBtn.style.backgroundColor = '#28a745';

        setTimeout(() => {
            copyBtn.textContent = originalText;
            copyBtn.style.backgroundColor = '';
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        const copyBtn = event.target;
        copyBtn.textContent = 'Copied!';
        setTimeout(() => copyBtn.textContent = 'Copy', 2000);
    });
}
</script>
{% endblock %}
