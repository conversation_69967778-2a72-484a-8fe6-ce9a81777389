{% extends "base.html" %}

{% block title %}Video Player{% endblock %}

{% block content %}
<div class="video-container">
    <div id="videoPlayer" class="video-player-container">
        <div class="loading">
            <div class="spinner"></div>
            <p>Loading video...</p>
        </div>
    </div>
</div>
<style>
.video-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: #000;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-player-container {
    width: 100%;
    height: 100vh;
    background: #000;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-player {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    background: #000;
    object-fit: contain;
}

.loading {
    text-align: center;
    color: #fff;
}

.spinner {
    border: 4px solid #333;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    background: #721c24;
    border: 1px solid #dc3545;
    color: #f8d7da;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    margin: 20px;
    max-width: 500px;
}
</style>
{% endblock %}

{% block scripts %}
<script>
const videoId = '{{ video_id }}';

document.addEventListener('DOMContentLoaded', function() {
    loadVideoPlayer();
});

async function loadVideoPlayer() {
    try {
        const response = await fetch(`/api/videos/${videoId}/stream`);
        const streamData = await response.json();

        if (response.ok) {
            // Check if redirect is required (first click)
            if (streamData.redirect_required) {
                console.log('🔗 First click redirect required');
                // Open redirect URL in new tab
                window.open(streamData.redirect_url, '_blank');

                // Show message to user
                showRedirectMessage();

                // Wait a moment then try to load video again
                setTimeout(async () => {
                    try {
                        const retryResponse = await fetch(`/api/videos/${videoId}/stream`);
                        const retryData = await retryResponse.json();

                        if (retryResponse.ok && !retryData.redirect_required) {
                            displayVideoPlayer(retryData.stream_url);
                            hideRedirectMessage();
                        } else {
                            showVideoError('Failed to load video after redirect');
                        }
                    } catch (error) {
                        console.error('Error on retry:', error);
                        showVideoError('Failed to load video after redirect');
                    }
                }, 3000); // Wait 3 seconds
            } else {
                displayVideoPlayer(streamData.stream_url);
            }
        } else {
            showVideoError(streamData.error || 'Failed to load video stream');
        }
    } catch (error) {
        console.error('Error loading video player:', error);
        showVideoError('Failed to load video stream');
    }
}

function displayVideoPlayer(url) {
    const videoPlayer = document.getElementById('videoPlayer');

    videoPlayer.innerHTML = `
        <video class="video-player" controls controlsList="nodownload" autoplay>
            <source src="${url}" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    `;

    const video = videoPlayer.querySelector('video');

    // Enable fullscreen on double-click
    video.addEventListener('dblclick', function() {
        if (video.requestFullscreen) {
            video.requestFullscreen();
        } else if (video.webkitRequestFullscreen) {
            video.webkitRequestFullscreen();
        } else if (video.mozRequestFullScreen) {
            video.mozRequestFullScreen();
        }
    });

    // Disable right-click context menu
    video.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
}

function setupRedirectButton() {
    const redirectBtn = document.getElementById('redirectBtn');
    const redirectInfo = document.getElementById('redirectInfo');
    
    redirectBtn.style.display = 'inline-block';
    redirectInfo.style.display = 'block';
    
    redirectBtn.addEventListener('click', async function() {
        if (!sessionId) {
            showError('Session expired. Please refresh the page.');
            return;
        }
        
        try {
            const response = await fetch(`/api/videos/${videoId}/redirect?session=${sessionId}`);
            
            if (response.redirected) {
                window.open(response.url, '_blank');
                redirectBtn.disabled = true;
                redirectBtn.textContent = 'Redirect Used';
                redirectInfo.textContent = 'You have used your redirect for this session.';
            } else {
                const result = await response.json();
                showError(result.error || 'Redirect failed');
                
                if (response.status === 429) {
                    redirectBtn.disabled = true;
                    redirectBtn.textContent = 'Redirect Used';
                    redirectInfo.textContent = 'You have used your redirect for this session.';
                }
            }
        } catch (error) {
            console.error('Redirect error:', error);
            showError('Redirect failed');
        }
    });
}

function loadNativeBanner() {
    const nativeBanner = document.getElementById('nativeBanner');
    
    // Load native banner HTML from environment variable
    const bannerHtml = `{{ native_banner_html | safe }}` || '';
    
    if (bannerHtml.trim()) {
        nativeBanner.innerHTML = bannerHtml;
    }
}

function showRedirectMessage() {
    const videoPlayer = document.getElementById('videoPlayer');

    videoPlayer.innerHTML = `
        <div class="redirect-message" style="text-align: center; padding: 40px; background: #1a1a1a; border-radius: 10px; margin: 20px 0; color: #fff;">
            <div class="loading-spinner" style="width: 40px; height: 40px; border: 4px solid #333; border-top: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <h4 style="color: #007bff; margin-bottom: 15px;">🔗 Opening monetization link...</h4>
            <p style="margin-bottom: 10px;">A new tab has been opened. The video will load automatically in a few seconds.</p>
            <small style="color: #888;">This happens only on your first visit to maximize revenue.</small>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
}

function hideRedirectMessage() {
    // Message will be replaced by video player
}

function showVideoError(message) {
    const videoInfo = document.getElementById('videoInfo');
    const videoPlayer = document.getElementById('videoPlayer');

    videoInfo.innerHTML = `
        <div class="error-message">
            <h2>Error</h2>
            <p>${message}</p>
        </div>
    `;

    videoPlayer.innerHTML = '';

    document.getElementById('redirectBtn').style.display = 'none';
    document.getElementById('redirectInfo').style.display = 'none';
}

function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const copyBtn = event.target;
        const originalText = copyBtn.textContent;
        copyBtn.textContent = 'Copied!';
        copyBtn.style.backgroundColor = '#28a745';

        setTimeout(() => {
            copyBtn.textContent = originalText;
            copyBtn.style.backgroundColor = '';
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        const copyBtn = event.target;
        copyBtn.textContent = 'Copied!';
        setTimeout(() => copyBtn.textContent = 'Copy', 2000);
    });
}
</script>
{% endblock %}
