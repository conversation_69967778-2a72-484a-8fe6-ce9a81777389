{% extends "base.html" %}

{% block title %}Anime Waifu - Video Collection{% endblock %}

{% block head %}
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        margin: 0;
        font-family: 'Arial', sans-serif;
    }

    .waifu-container {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 20px;
        box-sizing: border-box;
    }

    .waifu-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 100%;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .waifu-avatar {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        margin: 0 auto 30px;
        background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 80px;
        color: white;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        position: relative;
        overflow: hidden;
    }

    .waifu-avatar::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shine 3s infinite;
    }

    @keyframes shine {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .waifu-name {
        font-size: 2.5em;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .waifu-title {
        font-size: 1.2em;
        color: #666;
        margin-bottom: 20px;
        font-style: italic;
    }

    .waifu-bio {
        color: #555;
        line-height: 1.6;
        margin-bottom: 30px;
        font-size: 1.1em;
    }

    .social-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 30px;
    }

    .social-btn {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 15px 25px;
        border-radius: 50px;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        min-width: 120px;
        justify-content: center;
    }

    .telegram-btn {
        background: linear-gradient(45deg, #0088cc, #00aaff);
        color: white;
    }

    .telegram-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 136, 204, 0.4);
    }

    .facebook-btn {
        background: linear-gradient(45deg, #1877f2, #42a5f5);
        color: white;
    }

    .facebook-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(24, 119, 242, 0.4);
    }

    .videos-section {
        margin-top: 30px;
        padding-top: 30px;
        border-top: 2px solid rgba(102, 126, 234, 0.3);
    }

    .videos-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .video-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        text-align: left;
        transition: transform 0.3s ease;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .video-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .video-card h3 {
        color: #667eea;
        margin-bottom: 10px;
        font-size: 1.2em;
    }

    .video-card p {
        color: #666;
        font-size: 0.9em;
        margin-bottom: 15px;
    }

    .video-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.8em;
        color: #888;
    }

    .admin-link {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.9);
        padding: 10px 20px;
        border-radius: 25px;
        text-decoration: none;
        color: #667eea;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }

    .admin-link:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .waifu-container {
            padding: 10px;
        }

        .waifu-card {
            padding: 30px 20px;
        }

        .waifu-avatar {
            width: 150px;
            height: 150px;
            font-size: 60px;
        }

        .waifu-name {
            font-size: 2em;
        }

        .social-buttons {
            flex-direction: column;
            align-items: center;
        }

        .social-btn {
            width: 200px;
        }

        .videos-grid {
            grid-template-columns: 1fr;
        }

        .admin-link {
            position: static;
            display: block;
            text-align: center;
            margin: 20px auto;
            width: fit-content;
        }
    }

    @media (max-width: 480px) {
        .waifu-name {
            font-size: 1.8em;
        }

        .waifu-title {
            font-size: 1em;
        }

        .waifu-bio {
            font-size: 1em;
        }
    }
</style>
{% endblock %}

{% block content %}
<a href="/admin" class="admin-link">👑 Admin Panel</a>

<div class="waifu-container">
    <div class="waifu-card">
        <div class="waifu-avatar">
            🌸
        </div>

        <h1 class="waifu-name">Sakura-chan</h1>
        <p class="waifu-title">Your Anime Video Collection Waifu</p>

        <div class="waifu-bio">
            Welcome to my magical video collection! I'm here to share the most amazing anime content with you.
            Follow me on social media for exclusive updates and behind-the-scenes content! ✨
        </div>

        <div class="social-buttons">
            <a href="{{ telegram_link }}" class="social-btn telegram-btn" target="_blank">
                📱 Telegram
            </a>
            <a href="{{ facebook_link }}" class="social-btn facebook-btn" target="_blank">
                📘 Facebook
            </a>
        </div>

        <div class="videos-section">
            <h2 style="color: #667eea; margin-bottom: 10px;">✨ Latest Videos</h2>
            <div id="videoList" class="videos-grid">
                <div class="loading" style="grid-column: 1 / -1; text-align: center;">
                    <div class="spinner"></div>
                    <p>Loading magical videos...</p>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    loadRecentVideos();
});

async function loadRecentVideos() {
    try {
        const response = await fetch('/api/videos/recent');
        const videos = await response.json();

        const videoList = document.getElementById('videoList');

        if (videos.length === 0) {
            videoList.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; color: #888; padding: 40px;">
                    <div style="font-size: 3em; margin-bottom: 20px;">🎬</div>
                    <p>No magical videos yet! Check back soon for amazing content!</p>
                </div>
            `;
            return;
        }

        videoList.innerHTML = videos.map(video => `
            <div class="video-card">
                <h3><a href="/video/${video.id}" style="color: #667eea; text-decoration: none;">${video.title}</a></h3>
                <p>${video.description || 'A magical video experience awaits...'}</p>
                <div class="video-meta">
                    <span>👀 ${video.views || 0} views</span>
                    <span>📅 ${new Date(video.created_at).toLocaleDateString()}</span>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading videos:', error);
        document.getElementById('videoList').innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; color: #dc3545; padding: 40px;">
                <div style="font-size: 3em; margin-bottom: 20px;">😢</div>
                <p>Oops! Something went wrong loading the videos...</p>
            </div>
        `;
    }
}

// Add some sparkle effects
function createSparkles() {
    const sparkleContainer = document.createElement('div');
    sparkleContainer.style.position = 'fixed';
    sparkleContainer.style.top = '0';
    sparkleContainer.style.left = '0';
    sparkleContainer.style.width = '100%';
    sparkleContainer.style.height = '100%';
    sparkleContainer.style.pointerEvents = 'none';
    sparkleContainer.style.zIndex = '1';
    document.body.appendChild(sparkleContainer);

    setInterval(() => {
        if (Math.random() > 0.7) {
            const sparkle = document.createElement('div');
            sparkle.innerHTML = '✨';
            sparkle.style.position = 'absolute';
            sparkle.style.left = Math.random() * 100 + '%';
            sparkle.style.top = Math.random() * 100 + '%';
            sparkle.style.fontSize = (Math.random() * 20 + 10) + 'px';
            sparkle.style.animation = 'sparkleFloat 3s ease-out forwards';
            sparkleContainer.appendChild(sparkle);

            setTimeout(() => {
                sparkle.remove();
            }, 3000);
        }
    }, 500);
}

// Add sparkle animation CSS
const sparkleStyle = document.createElement('style');
sparkleStyle.textContent = `
    @keyframes sparkleFloat {
        0% {
            opacity: 1;
            transform: translateY(0) rotate(0deg);
        }
        100% {
            opacity: 0;
            transform: translateY(-100px) rotate(360deg);
        }
    }
`;
document.head.appendChild(sparkleStyle);

// Initialize sparkles
createSparkles();
</script>
{% endblock %}
