<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Video Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: #1a1a1a !important;
            border-bottom: 1px solid #333;
        }
        
        .card {
            background: #1a1a1a;
            border: 1px solid #333;
            color: #fff;
        }
        
        .card-header {
            background: #2a2a2a;
            border-bottom: 1px solid #333;
        }
        
        .table-dark {
            --bs-table-bg: #1a1a1a;
        }
        
        .btn-primary {
            background: #007bff;
            border-color: #007bff;
        }
        
        .btn-danger {
            background: #dc3545;
            border-color: #dc3545;
        }
        
        .alert {
            border: none;
            border-radius: 8px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .spinner-border {
            color: #007bff;
        }

        /* Upload Progress Enhancements */
        .progress {
            border-radius: 10px;
            overflow: hidden;
            background: #333;
        }

        .progress-bar {
            transition: width 0.3s ease;
        }

        .upload-stats-card {
            background: #2a2a2a;
            border: 1px solid #444;
            transition: all 0.3s ease;
        }

        .upload-stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 0.75rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .progress {
                height: 15px !important;
            }

            .btn-lg {
                padding: 0.75rem 1rem;
                font-size: 1rem;
            }

            .h5 {
                font-size: 1rem;
            }

            .h6 {
                font-size: 0.9rem;
            }

            .display-1 {
                font-size: 3rem;
            }

            .row.g-3 {
                --bs-gutter-x: 0.75rem;
                --bs-gutter-y: 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .container-fluid {
                padding: 0.5rem;
            }

            .card-body {
                padding: 0.75rem;
            }

            .upload-stats-card {
                padding: 0.75rem !important;
            }

            .upload-stats-card .small {
                font-size: 0.75rem;
            }

            .upload-stats-card .h5,
            .upload-stats-card .h6 {
                font-size: 0.85rem;
                margin-bottom: 0;
            }

            .alert {
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            .form-control-lg {
                padding: 0.75rem 1rem;
                font-size: 1rem;
            }

            .row.g-3 {
                --bs-gutter-x: 0.5rem;
                --bs-gutter-y: 0.5rem;
            }

            .progress {
                height: 12px !important;
            }
        }

        /* Animation for progress updates */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        .upload-active .upload-stats-card {
            animation: pulse 2s infinite;
        }

        /* Custom scrollbar for mobile */
        @media (max-width: 768px) {
            .table-responsive {
                scrollbar-width: thin;
                scrollbar-color: #6c757d #333;
            }

            .table-responsive::-webkit-scrollbar {
                height: 6px;
            }

            .table-responsive::-webkit-scrollbar-track {
                background: #333;
            }

            .table-responsive::-webkit-scrollbar-thumb {
                background: #6c757d;
                border-radius: 3px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-play-circle-fill me-2"></i>
                Video Platform Admin
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house me-1"></i>Home
                </a>
                <a class="nav-link" href="/admin/logout">
                    <i class="bi bi-box-arrow-right me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Alert Container -->
        <div id="alertContainer"></div>
        
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-camera-video fs-1"></i>
                        <h3 id="totalVideos">-</h3>
                        <p class="mb-0">Total Videos</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-eye fs-1"></i>
                        <h3 id="totalViews">-</h3>
                        <p class="mb-0">Total Views</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-people fs-1"></i>
                        <h3 id="uniqueViewers">-</h3>
                        <p class="mb-0">Unique Viewers</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-calendar-day fs-1"></i>
                        <h3 id="todayViews">-</h3>
                        <p class="mb-0">Today's Views</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="adminTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="videos-tab" data-bs-toggle="tab" data-bs-target="#videos" type="button" role="tab">
                                    <i class="bi bi-camera-video me-2"></i>Videos
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab">
                                    <i class="bi bi-cloud-upload me-2"></i>Upload
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button" role="tab">
                                    <i class="bi bi-graph-up me-2"></i>Analytics
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="adminTabsContent">
                            <!-- Videos Tab -->
                            <div class="tab-pane fade show active" id="videos" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0">Video Management</h5>
                                    <button class="btn btn-primary" onclick="loadVideos()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                    </button>
                                </div>
                                
                                <div id="videosContainer">
                                    <div class="loading">
                                        <div class="spinner-border" role="status"></div>
                                        <p class="mt-2">Loading videos...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Upload Tab -->
                            <div class="tab-pane fade" id="upload" role="tabpanel">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="mb-4">
                                            <i class="bi bi-cloud-upload me-2"></i>Upload New Video
                                        </h5>
                                    </div>
                                </div>

                                <form id="uploadForm" enctype="multipart/form-data">
                                    <div class="row">
                                        <!-- Upload Form Section -->
                                        <div class="col-lg-6 mb-4">
                                            <div class="card h-100">
                                                <div class="card-header">
                                                    <h6 class="card-title mb-0">
                                                        <i class="bi bi-file-earmark-play me-2"></i>Video Details
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <label for="videoFile" class="form-label">
                                                            <i class="bi bi-file-earmark-play me-1"></i>Video File
                                                        </label>
                                                        <input type="file" class="form-control form-control-lg" id="videoFile" name="video" accept="video/*" required>
                                                        <div class="form-text">
                                                            <i class="bi bi-info-circle me-1"></i>
                                                            Supported: MP4, AVI, MOV, WMV (Max: 500MB)
                                                        </div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="videoTitle" class="form-label">
                                                            <i class="bi bi-type me-1"></i>Title
                                                        </label>
                                                        <input type="text" class="form-control" id="videoTitle" name="title" required
                                                               placeholder="Enter video title...">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="videoDescription" class="form-label">
                                                            <i class="bi bi-card-text me-1"></i>Description
                                                        </label>
                                                        <textarea class="form-control" id="videoDescription" name="description" rows="4"
                                                                  placeholder="Enter video description (optional)..."></textarea>
                                                    </div>

                                                    <!-- Upload Button -->
                                                    <div class="d-grid gap-2">
                                                        <button type="submit" class="btn btn-primary btn-lg" id="uploadButton">
                                                            <i class="bi bi-cloud-upload me-2"></i>Start Upload
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger" id="cancelButton" style="display: none;">
                                                            <i class="bi bi-x-circle me-2"></i>Cancel Upload
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Upload Progress Section -->
                                        <div class="col-lg-6 mb-4">
                                            <div class="card h-100">
                                                <div class="card-header">
                                                    <h6 class="card-title mb-0">
                                                        <i class="bi bi-speedometer2 me-2"></i>Upload Progress
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div id="uploadProgressSection" style="display: none;">
                                                        <!-- Main Progress Bar -->
                                                        <div class="mb-4">
                                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                                <span class="text-muted">Progress</span>
                                                                <span class="fw-bold" id="uploadProgressText">0%</span>
                                                            </div>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                                                                     role="progressbar" style="width: 0%" id="uploadProgressBar">
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Progress Stats Grid -->
                                                        <div class="row g-3 mb-4">
                                                            <div class="col-6">
                                                                <div class="upload-stats-card rounded-3 p-3 text-center">
                                                                    <div class="text-muted small mb-1">
                                                                        <i class="bi bi-speedometer me-1"></i>Speed
                                                                    </div>
                                                                    <div class="h5 mb-0 text-primary" id="uploadSpeed">0 MB/s</div>
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="upload-stats-card rounded-3 p-3 text-center">
                                                                    <div class="text-muted small mb-1">
                                                                        <i class="bi bi-clock me-1"></i>ETA
                                                                    </div>
                                                                    <div class="h5 mb-0 text-info" id="uploadETA">--:--</div>
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="upload-stats-card rounded-3 p-3 text-center">
                                                                    <div class="text-muted small mb-1">
                                                                        <i class="bi bi-hdd me-1"></i>Size
                                                                    </div>
                                                                    <div class="h6 mb-0 text-success" id="uploadSize">0 / 0 MB</div>
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="upload-stats-card rounded-3 p-3 text-center">
                                                                    <div class="text-muted small mb-1">
                                                                        <i class="bi bi-stopwatch me-1"></i>Elapsed
                                                                    </div>
                                                                    <div class="h6 mb-0 text-warning" id="uploadElapsed">00:00</div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Status Message -->
                                                        <div class="alert alert-info mb-0" role="alert">
                                                            <i class="bi bi-info-circle me-2"></i>
                                                            <span id="uploadStatus">Ready to upload...</span>
                                                        </div>
                                                    </div>

                                                    <!-- Initial State -->
                                                    <div id="uploadInitialState">
                                                        <div class="text-center py-5">
                                                            <i class="bi bi-cloud-upload display-1 text-muted mb-3"></i>
                                                            <h5 class="text-muted">Select a video file to start uploading</h5>
                                                            <p class="text-muted">Upload progress will be shown here in real-time</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Analytics Tab -->
                            <div class="tab-pane fade" id="analytics" role="tabpanel">
                                <h5 class="mb-3">Analytics Overview</h5>
                                
                                <div id="analyticsContainer">
                                    <div class="loading">
                                        <div class="spinner-border" role="status"></div>
                                        <p class="mt-2">Loading analytics...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <script>
        // Global variables
        let currentVideos = [];
        let socket = null;
        let currentUploadSession = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing admin dashboard...');
            initializeSocket();
            loadDashboard();

            // Add cancel button functionality
            document.getElementById('cancelButton').addEventListener('click', function() {
                if (currentUploadSession) {
                    // Cancel upload (you can implement server-side cancellation if needed)
                    currentUploadSession = null;
                    resetUploadUI();
                    showAlert('Upload cancelled', 'warning');
                }
            });
        });
        
        // Initialize Socket.IO
        function initializeSocket() {
            socket = io({
                transports: ['polling', 'websocket'],
                upgrade: true,
                rememberUpgrade: true
            });

            socket.on('connect', function() {
                console.log('🔌 Connected to upload progress server');
                console.log('🔌 Socket ID:', socket.id);
            });

            socket.on('disconnect', function() {
                console.log('❌ Disconnected from upload progress server');
            });

            socket.on('connect_error', function(error) {
                console.error('❌ Socket connection error:', error);
            });

            socket.on('upload_progress', function(data) {
                console.log('📊 Received upload progress:', data);
                updateUploadProgress(data);
            });

            socket.on('upload_complete', function(data) {
                console.log('✅ Received upload complete:', data);
                handleUploadComplete(data);
            });

            socket.on('upload_error', function(data) {
                console.log('❌ Received upload error:', data);
                handleUploadError(data);
            });
        }

        // Load dashboard data
        async function loadDashboard() {
            try {
                await Promise.all([
                    loadStats(),
                    loadVideos()
                ]);
                console.log('✅ Dashboard loaded successfully');
            } catch (error) {
                console.error('❌ Error loading dashboard:', error);
                showAlert('Failed to load dashboard data', 'danger');
            }
        }
        
        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/admin/analytics', {
                    credentials: 'same-origin'
                });
                
                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = '/admin';
                        return;
                    }
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                const analytics = data.analytics || {};
                
                document.getElementById('totalVideos').textContent = analytics.total_videos || 0;
                document.getElementById('totalViews').textContent = analytics.total_views || 0;
                document.getElementById('uniqueViewers').textContent = analytics.unique_viewers || 0;
                document.getElementById('todayViews').textContent = analytics.views_today || 0;
                
            } catch (error) {
                console.error('Error loading stats:', error);
                // Set default values
                document.getElementById('totalVideos').textContent = '0';
                document.getElementById('totalViews').textContent = '0';
                document.getElementById('uniqueViewers').textContent = '0';
                document.getElementById('todayViews').textContent = '0';
            }
        }

        // Load videos
        async function loadVideos() {
            try {
                const response = await fetch('/api/admin/videos', {
                    credentials: 'same-origin'
                });

                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = '/admin';
                        return;
                    }
                    throw new Error(`HTTP ${response.status}`);
                }

                const videos = await response.json();
                currentVideos = videos;
                displayVideos(videos);

            } catch (error) {
                console.error('Error loading videos:', error);
                document.getElementById('videosContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Failed to load videos: ${error.message}
                    </div>
                `;
            }
        }

        // Display videos
        function displayVideos(videos) {
            const container = document.getElementById('videosContainer');

            if (!videos || videos.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-camera-video fs-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">No videos found</h5>
                        <p class="text-muted">Upload your first video to get started!</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Short URL</th>
                                <th>Views</th>
                                <th>Size</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            videos.forEach(video => {
                const createdDate = video.created_at ? new Date(video.created_at).toLocaleDateString() : 'Unknown';
                const fileSize = video.file_size ? (video.file_size / (1024*1024)).toFixed(2) + ' MB' : 'Unknown';
                const shortUrl = video.short_url || 'N/A';

                html += `
                    <tr>
                        <td>
                            <strong>${escapeHtml(video.title || 'Untitled')}</strong>
                            <br><small class="text-muted">${escapeHtml(video.description || 'No description')}</small>
                        </td>
                        <td>
                            <code>${shortUrl}</code>
                            ${shortUrl !== 'N/A' ? `<button class="btn btn-sm btn-outline-primary ms-1" onclick="copyToClipboard('${window.location.origin}/${shortUrl}')" title="Copy URL"><i class="bi bi-copy"></i></button>` : ''}
                        </td>
                        <td><span class="badge bg-info">${video.views || 0}</span></td>
                        <td>${fileSize}</td>
                        <td>${createdDate}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="/${shortUrl}" class="btn btn-outline-success" target="_blank" title="View Video">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <button onclick="deleteVideo('${video.id}')" class="btn btn-outline-danger" title="Delete">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
        }

        // Upload form handler
        document.addEventListener('DOMContentLoaded', function() {
            const uploadForm = document.getElementById('uploadForm');
            if (uploadForm) {
                uploadForm.addEventListener('submit', handleUpload);
            }
        });

        async function handleUpload(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const progressSection = document.getElementById('uploadProgressSection');
            const initialState = document.getElementById('uploadInitialState');
            const uploadButton = document.getElementById('uploadButton');
            const cancelButton = document.getElementById('cancelButton');

            try {
                // Show progress section and hide initial state
                progressSection.style.display = 'block';
                initialState.style.display = 'none';
                uploadButton.style.display = 'none';
                cancelButton.style.display = 'block';

                resetUploadProgress();

                const response = await fetch('/api/videos/upload-with-progress', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const result = await response.json();
                    currentUploadSession = result.session_id;
                    console.log('📤 Upload started with session:', currentUploadSession);
                    console.log('🔌 Socket connected:', socket.connected);
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Upload failed');
                }

            } catch (error) {
                console.error('Upload error:', error);
                showAlert(`Upload failed: ${error.message}`, 'danger');
                resetUploadUI();
            }
        }

        // Update upload progress
        function updateUploadProgress(data) {
            if (data.session_id !== currentUploadSession) return;

            const progressBar = document.getElementById('uploadProgressBar');
            const progressText = document.getElementById('uploadProgressText');
            const statusText = document.getElementById('uploadStatus');
            const speedText = document.getElementById('uploadSpeed');
            const etaText = document.getElementById('uploadETA');
            const sizeText = document.getElementById('uploadSize');
            const elapsedText = document.getElementById('uploadElapsed');

            // Update progress bar and percentage
            const percent = Math.round(data.progress_percent);
            progressBar.style.width = percent + '%';
            progressText.textContent = percent + '%';

            // Update status
            statusText.textContent = data.current_step || 'Uploading...';

            // Update speed
            const speed = data.speed_mbps || 0;
            speedText.textContent = speed.toFixed(2) + ' MB/s';

            // Update ETA
            const eta = data.eta_seconds || 0;
            etaText.textContent = formatTime(eta);

            // Update size
            const uploadedMB = (data.uploaded_mb || 0).toFixed(2);
            const totalMB = (data.total_mb || 0).toFixed(2);
            sizeText.textContent = `${uploadedMB} / ${totalMB} MB`;

            // Update elapsed time
            const elapsed = data.elapsed_time || 0;
            elapsedText.textContent = formatTime(elapsed);

            // Update progress bar color based on progress
            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
            if (percent < 30) {
                progressBar.classList.add('bg-danger');
            } else if (percent < 70) {
                progressBar.classList.add('bg-warning');
            } else {
                progressBar.classList.add('bg-success');
            }
        }

        // Handle upload completion
        function handleUploadComplete(data) {
            if (data.session_id !== currentUploadSession) return;

            // Update progress to 100%
            const progressBar = document.getElementById('uploadProgressBar');
            const progressText = document.getElementById('uploadProgressText');
            const statusText = document.getElementById('uploadStatus');

            progressBar.style.width = '100%';
            progressBar.className = 'progress-bar bg-success';
            progressText.textContent = '100%';
            statusText.textContent = 'Upload completed successfully!';

            showAlert('Video uploaded successfully!', 'success');

            // Reset form and UI after a delay
            setTimeout(() => {
                document.getElementById('uploadForm').reset();
                resetUploadUI();
                currentUploadSession = null;

                // Refresh data
                loadStats();
                loadVideos();
            }, 2000);
        }

        // Handle upload error
        function handleUploadError(data) {
            if (data.session_id !== currentUploadSession) return;

            showAlert(`Upload failed: ${data.error}`, 'danger');
            resetUploadUI();
            currentUploadSession = null;
        }

        // Reset upload progress
        function resetUploadProgress() {
            const progressBar = document.getElementById('uploadProgressBar');
            const progressText = document.getElementById('uploadProgressText');
            const statusText = document.getElementById('uploadStatus');
            const speedText = document.getElementById('uploadSpeed');
            const etaText = document.getElementById('uploadETA');
            const sizeText = document.getElementById('uploadSize');
            const elapsedText = document.getElementById('uploadElapsed');

            progressBar.style.width = '0%';
            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-primary';
            progressText.textContent = '0%';
            statusText.textContent = 'Preparing upload...';
            speedText.textContent = '0 MB/s';
            etaText.textContent = '--:--';
            sizeText.textContent = '0 / 0 MB';
            elapsedText.textContent = '00:00';
        }

        // Reset upload UI
        function resetUploadUI() {
            const progressSection = document.getElementById('uploadProgressSection');
            const initialState = document.getElementById('uploadInitialState');
            const uploadButton = document.getElementById('uploadButton');
            const cancelButton = document.getElementById('cancelButton');

            progressSection.style.display = 'none';
            initialState.style.display = 'block';
            uploadButton.style.display = 'block';
            cancelButton.style.display = 'none';
        }

        // Format time in MM:SS format
        function formatTime(seconds) {
            if (!seconds || seconds === 0 || !isFinite(seconds)) {
                return '--:--';
            }

            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // Delete video
        async function deleteVideo(videoId) {
            if (!confirm('Are you sure you want to delete this video?')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/videos/${videoId}`, {
                    method: 'DELETE',
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    showAlert('Video deleted successfully!', 'success');
                    await loadStats();
                    await loadVideos();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Delete failed');
                }

            } catch (error) {
                console.error('Delete error:', error);
                showAlert(`Delete failed: ${error.message}`, 'danger');
            }
        }

        // Utility functions
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();

            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showAlert('URL copied to clipboard!', 'success');
            }).catch(() => {
                showAlert('Failed to copy URL', 'danger');
            });
        }
    </script>
</body>
</html>
