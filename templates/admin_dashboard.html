<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Video Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: #0a0a0a;
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: #1a1a1a !important;
            border-bottom: 1px solid #333;
        }
        
        .card {
            background: #1a1a1a;
            border: 1px solid #333;
            color: #fff;
        }
        
        .card-header {
            background: #2a2a2a;
            border-bottom: 1px solid #333;
        }
        
        .table-dark {
            --bs-table-bg: #1a1a1a;
        }
        
        .btn-primary {
            background: #007bff;
            border-color: #007bff;
        }
        
        .btn-danger {
            background: #dc3545;
            border-color: #dc3545;
        }
        
        .alert {
            border: none;
            border-radius: 8px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .spinner-border {
            color: #007bff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-play-circle-fill me-2"></i>
                Video Platform Admin
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house me-1"></i>Home
                </a>
                <a class="nav-link" href="/admin/logout">
                    <i class="bi bi-box-arrow-right me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Alert Container -->
        <div id="alertContainer"></div>
        
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-camera-video fs-1"></i>
                        <h3 id="totalVideos">-</h3>
                        <p class="mb-0">Total Videos</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-eye fs-1"></i>
                        <h3 id="totalViews">-</h3>
                        <p class="mb-0">Total Views</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-people fs-1"></i>
                        <h3 id="uniqueViewers">-</h3>
                        <p class="mb-0">Unique Viewers</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-calendar-day fs-1"></i>
                        <h3 id="todayViews">-</h3>
                        <p class="mb-0">Today's Views</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="adminTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="videos-tab" data-bs-toggle="tab" data-bs-target="#videos" type="button" role="tab">
                                    <i class="bi bi-camera-video me-2"></i>Videos
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab">
                                    <i class="bi bi-cloud-upload me-2"></i>Upload
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button" role="tab">
                                    <i class="bi bi-graph-up me-2"></i>Analytics
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="adminTabsContent">
                            <!-- Videos Tab -->
                            <div class="tab-pane fade show active" id="videos" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0">Video Management</h5>
                                    <button class="btn btn-primary" onclick="loadVideos()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                    </button>
                                </div>
                                
                                <div id="videosContainer">
                                    <div class="loading">
                                        <div class="spinner-border" role="status"></div>
                                        <p class="mt-2">Loading videos...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Upload Tab -->
                            <div class="tab-pane fade" id="upload" role="tabpanel">
                                <h5 class="mb-3">Upload New Video</h5>
                                
                                <form id="uploadForm" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="videoFile" class="form-label">Video File</label>
                                                <input type="file" class="form-control" id="videoFile" name="video" accept="video/*" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="videoTitle" class="form-label">Title</label>
                                                <input type="text" class="form-control" id="videoTitle" name="title" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="videoDescription" class="form-label">Description</label>
                                                <textarea class="form-control" id="videoDescription" name="description" rows="3"></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Upload Progress</label>
                                                <div class="progress" style="display: none;" id="uploadProgress">
                                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="bi bi-cloud-upload me-1"></i>Upload Video
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Analytics Tab -->
                            <div class="tab-pane fade" id="analytics" role="tabpanel">
                                <h5 class="mb-3">Analytics Overview</h5>
                                
                                <div id="analyticsContainer">
                                    <div class="loading">
                                        <div class="spinner-border" role="status"></div>
                                        <p class="mt-2">Loading analytics...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let currentVideos = [];
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing admin dashboard...');
            loadDashboard();
        });
        
        // Load dashboard data
        async function loadDashboard() {
            try {
                await Promise.all([
                    loadStats(),
                    loadVideos()
                ]);
                console.log('✅ Dashboard loaded successfully');
            } catch (error) {
                console.error('❌ Error loading dashboard:', error);
                showAlert('Failed to load dashboard data', 'danger');
            }
        }
        
        // Load statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/admin/analytics', {
                    credentials: 'same-origin'
                });
                
                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = '/admin';
                        return;
                    }
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                const analytics = data.analytics || {};
                
                document.getElementById('totalVideos').textContent = analytics.total_videos || 0;
                document.getElementById('totalViews').textContent = analytics.total_views || 0;
                document.getElementById('uniqueViewers').textContent = analytics.unique_viewers || 0;
                document.getElementById('todayViews').textContent = analytics.views_today || 0;
                
            } catch (error) {
                console.error('Error loading stats:', error);
                // Set default values
                document.getElementById('totalVideos').textContent = '0';
                document.getElementById('totalViews').textContent = '0';
                document.getElementById('uniqueViewers').textContent = '0';
                document.getElementById('todayViews').textContent = '0';
            }
        }

        // Load videos
        async function loadVideos() {
            try {
                const response = await fetch('/api/admin/videos', {
                    credentials: 'same-origin'
                });

                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = '/admin';
                        return;
                    }
                    throw new Error(`HTTP ${response.status}`);
                }

                const videos = await response.json();
                currentVideos = videos;
                displayVideos(videos);

            } catch (error) {
                console.error('Error loading videos:', error);
                document.getElementById('videosContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Failed to load videos: ${error.message}
                    </div>
                `;
            }
        }

        // Display videos
        function displayVideos(videos) {
            const container = document.getElementById('videosContainer');

            if (!videos || videos.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-camera-video fs-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">No videos found</h5>
                        <p class="text-muted">Upload your first video to get started!</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Short URL</th>
                                <th>Views</th>
                                <th>Size</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            videos.forEach(video => {
                const createdDate = video.created_at ? new Date(video.created_at).toLocaleDateString() : 'Unknown';
                const fileSize = video.file_size ? (video.file_size / (1024*1024)).toFixed(2) + ' MB' : 'Unknown';
                const shortUrl = video.short_url || 'N/A';

                html += `
                    <tr>
                        <td>
                            <strong>${escapeHtml(video.title || 'Untitled')}</strong>
                            <br><small class="text-muted">${escapeHtml(video.description || 'No description')}</small>
                        </td>
                        <td>
                            <code>${shortUrl}</code>
                            ${shortUrl !== 'N/A' ? `<button class="btn btn-sm btn-outline-primary ms-1" onclick="copyToClipboard('${window.location.origin}/${shortUrl}')" title="Copy URL"><i class="bi bi-copy"></i></button>` : ''}
                        </td>
                        <td><span class="badge bg-info">${video.views || 0}</span></td>
                        <td>${fileSize}</td>
                        <td>${createdDate}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="/${shortUrl}" class="btn btn-outline-success" target="_blank" title="View Video">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <button onclick="deleteVideo('${video.id}')" class="btn btn-outline-danger" title="Delete">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
        }

        // Upload form handler
        document.addEventListener('DOMContentLoaded', function() {
            const uploadForm = document.getElementById('uploadForm');
            if (uploadForm) {
                uploadForm.addEventListener('submit', handleUpload);
            }
        });

        async function handleUpload(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const progressBar = document.querySelector('#uploadProgress .progress-bar');
            const progressContainer = document.getElementById('uploadProgress');

            try {
                progressContainer.style.display = 'block';
                progressBar.style.width = '0%';

                const response = await fetch('/api/videos/upload', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });

                progressBar.style.width = '100%';

                if (response.ok) {
                    const result = await response.json();
                    showAlert('Video uploaded successfully!', 'success');
                    event.target.reset();
                    progressContainer.style.display = 'none';

                    // Refresh data
                    await loadStats();
                    await loadVideos();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Upload failed');
                }

            } catch (error) {
                console.error('Upload error:', error);
                showAlert(`Upload failed: ${error.message}`, 'danger');
                progressContainer.style.display = 'none';
            }
        }

        // Delete video
        async function deleteVideo(videoId) {
            if (!confirm('Are you sure you want to delete this video?')) {
                return;
            }

            try {
                const response = await fetch(`/api/admin/videos/${videoId}`, {
                    method: 'DELETE',
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    showAlert('Video deleted successfully!', 'success');
                    await loadStats();
                    await loadVideos();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Delete failed');
                }

            } catch (error) {
                console.error('Delete error:', error);
                showAlert(`Delete failed: ${error.message}`, 'danger');
            }
        }

        // Utility functions
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();

            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showAlert('URL copied to clipboard!', 'success');
            }).catch(() => {
                showAlert('Failed to copy URL', 'danger');
            });
        }
    </script>
</body>
</html>
