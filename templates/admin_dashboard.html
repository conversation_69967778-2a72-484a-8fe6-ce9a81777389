{% extends "base.html" %}

{% block title %}Admin Dashboard - Video Platform{% endblock %}

{% block head %}
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- DataTables -->
<link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="#">
            <i class="bi bi-play-circle-fill me-2"></i>
            Video Platform Admin
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="#dashboard" data-bs-toggle="pill">
                        <i class="bi bi-speedometer2 me-1"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#videos" data-bs-toggle="pill">
                        <i class="bi bi-collection-play me-1"></i>Videos
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#upload" data-bs-toggle="pill">
                        <i class="bi bi-cloud-upload me-1"></i>Upload
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#analytics" data-bs-toggle="pill">
                        <i class="bi bi-graph-up me-1"></i>Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings" data-bs-toggle="pill">
                        <i class="bi bi-gear me-1"></i>Settings
                    </a>
                </li>
            </ul>

            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>Admin
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/"><i class="bi bi-house me-2"></i>View Site</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/admin/logout"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Tab Content -->
    <div class="tab-content" id="adminTabContent">

        <!-- Dashboard Tab -->
        <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4"><i class="bi bi-speedometer2 me-2"></i>Dashboard Overview</h2>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4" id="statsCards">
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading analytics...</p>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-graph-up me-2"></i>Daily Views</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="viewsChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-pie-chart me-2"></i>Video Status</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentActivity">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                    <span class="ms-2">Loading recent activity...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Videos Management Tab -->
        <div class="tab-pane fade" id="videos" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4"><i class="bi bi-collection-play me-2"></i>Video Management</h2>
                </div>
            </div>

            <!-- Video Controls -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="videoSearch" placeholder="Search videos...">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshVideos()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                            <i class="bi bi-trash me-1"></i>Bulk Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- Videos Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="videosTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>Thumbnail</th>
                                    <th>Title</th>
                                    <th>Short URL</th>
                                    <th>Views</th>
                                    <th>Size</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="videosTableBody">
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <p class="mt-2">Loading videos...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Tab -->
        <div class="tab-pane fade" id="upload" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4"><i class="bi bi-cloud-upload me-2"></i>Upload New Video</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-file-earmark-play me-2"></i>Video Upload</h5>
                        </div>
                        <div class="card-body">
                            <form id="uploadForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="videoFile" class="form-label">
                                        <i class="bi bi-file-earmark-arrow-up me-1"></i>Select Video File
                                    </label>
                                    <input type="file" class="form-control" id="videoFile" name="video" accept="video/*" required>
                                    <div class="form-text">Supported formats: MP4, AVI, MOV, WMV (Max: 500MB)</div>
                                </div>

                                <div class="mb-3">
                                    <label for="videoTitle" class="form-label">
                                        <i class="bi bi-type me-1"></i>Video Title
                                    </label>
                                    <input type="text" class="form-control" id="videoTitle" name="title" required>
                                </div>

                                <div class="mb-3">
                                    <label for="videoDescription" class="form-label">
                                        <i class="bi bi-text-paragraph me-1"></i>Description (optional)
                                    </label>
                                    <textarea class="form-control" id="videoDescription" name="description" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="videoExpiry" class="form-label">
                                                <i class="bi bi-calendar-event me-1"></i>Expiry Date (optional)
                                            </label>
                                            <input type="date" class="form-control" id="videoExpiry" name="expiry">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="videoTags" class="form-label">
                                                <i class="bi bi-tags me-1"></i>Tags (optional)
                                            </label>
                                            <input type="text" class="form-control" id="videoTags" name="tags" placeholder="anime, action, episode1">
                                        </div>
                                    </div>
                                </div>

                                <!-- Upload Progress -->
                                <div class="mb-3" id="uploadProgress" style="display: none;">
                                    <label class="form-label">Upload Progress</label>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                             id="progressBar" role="progressbar" style="width: 0%">
                                            <span id="progressText">0%</span>
                                        </div>
                                    </div>
                                    <small class="text-muted" id="uploadStatus">Preparing upload...</small>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn">
                                        <i class="bi bi-cloud-upload me-2"></i>
                                        <span class="btn-text">Upload Video</span>
                                        <span class="btn-spinner d-none">
                                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                            Uploading...
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Upload Tips -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-lightbulb me-2"></i>Upload Tips</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>Use descriptive titles</li>
                                <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>Add relevant tags</li>
                                <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>Set expiry for temporary content</li>
                                <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>Compress videos for faster upload</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Storage Info -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-hdd me-2"></i>Storage Info</h5>
                        </div>
                        <div class="card-body">
                            <div id="storageInfo">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                    <p class="mt-2 mb-0">Loading storage info...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-pane fade" id="analytics" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4"><i class="bi bi-graph-up me-2"></i>Advanced Analytics</h2>
                </div>
            </div>

            <!-- Analytics Controls -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <select class="form-select" id="analyticsTimeRange">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                </div>
                <div class="col-md-8 text-end">
                    <button class="btn btn-outline-primary" onclick="exportAnalytics()">
                        <i class="bi bi-download me-1"></i>Export Data
                    </button>
                </div>
            </div>

            <!-- Detailed Analytics -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-eye me-2"></i>View Analytics</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="detailedViewsChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-geo-alt me-2"></i>Geographic Data</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="geoChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Videos -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-trophy me-2"></i>Top Performing Videos</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Rank</th>
                                            <th>Video</th>
                                            <th>Views</th>
                                            <th>Unique Views</th>
                                            <th>Engagement</th>
                                        </tr>
                                    </thead>
                                    <tbody id="topVideosTable">
                                        <tr>
                                            <td colspan="5" class="text-center">
                                                <div class="spinner-border text-primary" role="status"></div>
                                                <p class="mt-2">Loading top videos...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-pane fade" id="settings" role="tabpanel">
            <div class="row">
                <div class="col-12">
                    <h2 class="mb-4"><i class="bi bi-gear me-2"></i>Platform Settings</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <!-- General Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-sliders me-2"></i>General Settings</h5>
                        </div>
                        <div class="card-body">
                            <form id="generalSettingsForm">
                                <div class="mb-3">
                                    <label class="form-label">Default Video Expiry</label>
                                    <select class="form-select" id="defaultExpiry">
                                        <option value="7">7 days</option>
                                        <option value="30" selected>30 days</option>
                                        <option value="90">90 days</option>
                                        <option value="365">1 year</option>
                                        <option value="0">Never expire</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Max File Size (MB)</label>
                                    <input type="number" class="form-control" id="maxFileSize" value="500" min="1" max="2048">
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableAnalytics" checked>
                                        <label class="form-check-label" for="enableAnalytics">
                                            Enable Analytics Tracking
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-1"></i>Save Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <!-- Database Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-database me-2"></i>Database Status</h5>
                        </div>
                        <div class="card-body">
                            <div id="databaseStatus">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p class="mt-2">Loading database status...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Info -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="bi bi-info-circle me-2"></i>System Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h6 class="text-muted">Platform Version</h6>
                                    <p class="mb-0">v2.0.0</p>
                                </div>
                                <div class="col-6">
                                    <h6 class="text-muted">Uptime</h6>
                                    <p class="mb-0" id="systemUptime">Loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<style>
/* Custom Dark Theme for Admin Panel */
body {
    background-color: #0d1117;
    color: #e6edf3;
}

.navbar-dark {
    background-color: #161b22 !important;
}

.card {
    background-color: #21262d;
    border: 1px solid #30363d;
    color: #e6edf3;
}

.card-header {
    background-color: #161b22;
    border-bottom: 1px solid #30363d;
}

.table-dark {
    --bs-table-bg: #161b22;
}

.btn-outline-primary {
    color: #58a6ff;
    border-color: #58a6ff;
}

.btn-outline-primary:hover {
    background-color: #58a6ff;
    border-color: #58a6ff;
}

.btn-outline-danger {
    color: #f85149;
    border-color: #f85149;
}

.btn-outline-danger:hover {
    background-color: #f85149;
    border-color: #f85149;
}

.form-control, .form-select {
    background-color: #0d1117;
    border: 1px solid #30363d;
    color: #e6edf3;
}

.form-control:focus, .form-select:focus {
    background-color: #0d1117;
    border-color: #58a6ff;
    color: #e6edf3;
    box-shadow: 0 0 0 0.25rem rgba(88, 166, 255, 0.25);
}

.progress {
    background-color: #21262d;
}

.text-muted {
    color: #7d8590 !important;
}

/* Additional custom styles */
.stat-card {
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.video-thumbnail {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.badge-status {
    font-size: 0.75rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(88, 166, 255, 0.1);
}
    margin-bottom: 10px;
}

</style>
{% endblock %}

{% block scripts %}
<script>
// Global variables
let videosTable;
let viewsChart;
let statusChart;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the admin panel
    initializeAdminPanel();

    // Load initial data
    loadDashboardData();

    // Setup event listeners
    setupEventListeners();
});

function initializeAdminPanel() {
    try {
        console.log('Initializing admin panel...');
        // Initialize DataTable for videos with proper column configuration
        videosTable = $('#videosTable').DataTable({
        responsive: true,
        pageLength: 10,
        order: [[7, 'desc']], // Sort by created date (8th column, 0-indexed = 7)
        columns: [
            { title: "Select", orderable: false, searchable: false, width: "50px" },
            { title: "Thumbnail", orderable: false, searchable: false, width: "80px" },
            { title: "Title", orderable: true, searchable: true },
            { title: "Short URL", orderable: false, searchable: true },
            { title: "Views", orderable: true, searchable: false, width: "80px" },
            { title: "Size", orderable: true, searchable: false, width: "100px" },
            { title: "Status", orderable: true, searchable: true, width: "100px" },
            { title: "Created", orderable: true, searchable: false, width: "120px" },
            { title: "Actions", orderable: false, searchable: false, width: "150px" }
        ],
        columnDefs: [
            { targets: [0, 1, 8], orderable: false, searchable: false },
            { targets: [4, 5, 7], className: "text-center" }
        ],
        language: {
            emptyTable: "No videos found",
            zeroRecords: "No matching videos found",
            info: "Showing _START_ to _END_ of _TOTAL_ videos",
            infoEmpty: "Showing 0 to 0 of 0 videos",
            infoFiltered: "(filtered from _MAX_ total videos)"
        },
        dom: 'rtip', // Remove default search box since we have custom one
        processing: false,
        deferRender: true
    });

    console.log('DataTable initialized successfully');
    } catch (error) {
        console.error('Error initializing DataTable:', error);
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupEventListeners() {
    // Upload form handler
    document.getElementById('uploadForm').addEventListener('submit', handleVideoUpload);

    // Select all checkbox
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="videoSelect"]');
        checkboxes.forEach(cb => cb.checked = this.checked);
    });

    // Search functionality
    document.getElementById('videoSearch').addEventListener('keyup', function() {
        videosTable.search(this.value).draw();
    });

    // Tab change handler
    document.querySelectorAll('[data-bs-toggle="pill"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('href');
            if (target === '#analytics') {
                loadAdvancedAnalytics();
            } else if (target === '#settings') {
                loadSettings();
            }
        });
    });
}

async function loadDashboardData() {
    await Promise.all([
        loadAnalytics(),
        loadVideos(),
        loadStorageInfo(),
        loadDatabaseStatus()
    ]);
}

async function loadAnalytics() {
    try {
        const response = await fetch('/api/admin/analytics');
        const data = await response.json();

        if (response.ok) {
            displayStats(data.analytics, data.storage);
            displayCharts(data.analytics);
            displayRecentActivity(data.analytics.recent_activity || []);
        } else {
            showError('Failed to load analytics');
        }
    } catch (error) {
        console.error('Error loading analytics:', error);
        showError('Failed to load analytics');
    }
}

function displayStats(analytics, storage) {
    const statsCards = document.getElementById('statsCards');

    statsCards.innerHTML = `
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="bi bi-collection-play fs-1 text-primary mb-2"></i>
                    <h3 class="card-title">${analytics.total_videos || 0}</h3>
                    <p class="card-text text-muted">Total Videos</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="bi bi-eye fs-1 text-success mb-2"></i>
                    <h3 class="card-title">${analytics.total_views || 0}</h3>
                    <p class="card-text text-muted">Total Views</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="bi bi-people fs-1 text-info mb-2"></i>
                    <h3 class="card-title">${analytics.total_unique_views || 0}</h3>
                    <p class="card-text text-muted">Unique Views</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="bi bi-hdd fs-1 text-warning mb-2"></i>
                    <h3 class="card-title">${storage.total_size_gb || 0} GB</h3>
                    <p class="card-text text-muted">Storage Used</p>
                </div>
            </div>
        </div>
    `;
}

function displayCharts(analytics) {
    // Views Chart
    const viewsCtx = document.getElementById('viewsChart').getContext('2d');
    if (viewsChart) viewsChart.destroy();

    const dates = Object.keys(analytics.daily_stats || {}).sort().slice(-7);
    const views = dates.map(date => analytics.daily_stats[date] || 0);

    viewsChart = new Chart(viewsCtx, {
        type: 'line',
        data: {
            labels: dates.map(date => new Date(date).toLocaleDateString()),
            datasets: [{
                label: 'Daily Views',
                data: views,
                borderColor: '#58a6ff',
                backgroundColor: 'rgba(88, 166, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: { color: '#e6edf3' }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { color: '#e6edf3' },
                    grid: { color: '#30363d' }
                },
                x: {
                    ticks: { color: '#e6edf3' },
                    grid: { color: '#30363d' }
                }
            }
        }
    });

    // Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    if (statusChart) statusChart.destroy();

    statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active', 'Expired', 'Processing'],
            datasets: [{
                data: [
                    analytics.active_videos || 0,
                    analytics.expired_videos || 0,
                    analytics.processing_videos || 0
                ],
                backgroundColor: ['#28a745', '#dc3545', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: { color: '#e6edf3' }
                }
            }
        }
    });
}

async function loadVideos() {
    try {
        console.log('Loading videos...');
        const response = await fetch('/api/admin/videos');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const videos = await response.json();
        console.log('Videos loaded:', videos);

        // Clear existing data
        if (videosTable) {
            videosTable.clear();
        } else {
            console.error('videosTable not initialized');
            return;
        }

        if (!videos || videos.length === 0) {
            console.log('No videos found');
            videosTable.draw();
            return;
        }

        // Add videos to DataTable with proper error handling
        videos.forEach((video, index) => {
            try {
                const shortUrl = video.short_url ? `${window.location.origin}/${video.short_url}` : 'N/A';
                const statusBadge = getStatusBadge(video.status || 'active');
                const fileSize = video.file_size ? (video.file_size / (1024*1024)).toFixed(2) : '0.00';
                const createdDate = video.created_at ? new Date(video.created_at).toLocaleDateString() : 'Unknown';

                // Ensure all data is properly escaped and formatted
                const rowData = [
                    `<input type="checkbox" name="videoSelect" value="${video.id || ''}">`,
                    `<img src="/api/videos/${video.id}/thumbnail" class="video-thumbnail" alt="Thumbnail" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjMzMzIi8+Cjx0ZXh0IHg9IjMwIiB5PSIyMCIgZmlsbD0iIzk5OSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Tm8gSW1hZ2U8L3RleHQ+Cjwvc3ZnPg=='">`,
                    `<div>
                        <strong>${escapeHtml(video.title || 'Untitled')}</strong>
                        <br><small class="text-muted">${escapeHtml(video.description || 'No description')}</small>
                    </div>`,
                    `<div>
                        <code class="text-break">${video.short_url || 'N/A'}</code>
                        ${video.short_url ? `<button class="btn btn-sm btn-outline-primary ms-1" onclick="copyToClipboard('${shortUrl}')" data-bs-toggle="tooltip" title="Copy URL"><i class="bi bi-copy"></i></button>` : ''}
                    </div>`,
                    `<span class="badge bg-info">${video.views || 0}</span>`,
                    `${fileSize} MB`,
                    statusBadge,
                    createdDate,
                    `<div class="btn-group btn-group-sm" role="group">
                        <a href="/video/${video.id}" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="View Video">
                            <i class="bi bi-eye"></i>
                        </a>
                        <a href="/${video.short_url || video.id}" class="btn btn-outline-success" data-bs-toggle="tooltip" title="Short URL" target="_blank">
                            <i class="bi bi-link-45deg"></i>
                        </a>
                        <button onclick="editVideo('${video.id}')" class="btn btn-outline-warning" data-bs-toggle="tooltip" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button onclick="deleteVideo('${video.id}')" class="btn btn-outline-danger" data-bs-toggle="tooltip" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>`
                ];

                videosTable.row.add(rowData);
            } catch (rowError) {
                console.error(`Error processing video ${index}:`, rowError, video);
            }
        });

        // Draw the table and reinitialize tooltips
        videosTable.draw();

        // Reinitialize tooltips for new content
        setTimeout(() => {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }, 100);

    } catch (error) {
        console.error('Error loading videos:', error);
        showError('Failed to load videos: ' + error.message);
    }
}

async function handleVideoUpload(event) {
    event.preventDefault();

    const formData = new FormData();
    const videoFile = document.getElementById('videoFile').files[0];
    const title = document.getElementById('videoTitle').value;
    const description = document.getElementById('videoDescription').value;

    if (!videoFile) {
        showError('Please select a video file');
        return;
    }

    formData.append('video', videoFile);
    formData.append('title', title);
    formData.append('description', description);

    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    uploadProgress.style.display = 'block';

    try {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressText.textContent = `Uploading... ${Math.round(percentComplete)}%`;
            }
        });

        xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                showSuccess('Video uploaded successfully!');
                document.getElementById('uploadForm').reset();
                uploadProgress.style.display = 'none';
                loadVideos(); // Reload videos list
                loadAnalytics(); // Reload analytics
            } else {
                const response = JSON.parse(xhr.responseText);
                showError(response.error || 'Upload failed. Please try again.');
                uploadProgress.style.display = 'none';
            }
        });

        xhr.addEventListener('error', function() {
            showError('Upload failed. Please try again.');
            uploadProgress.style.display = 'none';
        });

        xhr.open('POST', '/api/videos/upload');
        xhr.send(formData);

    } catch (error) {
        console.error('Upload error:', error);
        showError('Upload failed. Please try again.');
        uploadProgress.style.display = 'none';
    }
}

async function deleteVideo(videoId) {
    if (!confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/videos/${videoId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
            showSuccess('Video deleted successfully');
            loadVideos(); // Reload videos list
            loadAnalytics(); // Reload analytics
        } else {
            showError(result.error || 'Failed to delete video');
        }
    } catch (error) {
        console.error('Error deleting video:', error);
        showError('Failed to delete video');
    }
}

// Enhanced utility functions
function getStatusBadge(status) {
    const badges = {
        'active': '<span class="badge bg-success">Active</span>',
        'expired': '<span class="badge bg-danger">Expired</span>',
        'processing': '<span class="badge bg-warning">Processing</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccess('URL copied to clipboard!');
    }).catch(() => {
        showError('Failed to copy URL');
    });
}

function refreshVideos() {
    loadVideos();
    showSuccess('Videos refreshed');
}

function bulkDelete() {
    const selected = document.querySelectorAll('input[name="videoSelect"]:checked');
    if (selected.length === 0) {
        showError('Please select videos to delete');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${selected.length} video(s)?`)) {
        return;
    }

    showSuccess(`Bulk delete functionality coming soon!`);
}

function editVideo(videoId) {
    showSuccess('Edit functionality coming soon!');
}

async function loadStorageInfo() {
    try {
        const response = await fetch('/api/admin/analytics');
        const data = await response.json();

        if (response.ok && data.storage) {
            const storageInfo = document.getElementById('storageInfo');
            storageInfo.innerHTML = `
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-muted">Used</h6>
                        <p class="mb-0">${data.storage.total_size_gb || 0} GB</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted">Files</h6>
                        <p class="mb-0">${data.storage.total_files || 0}</p>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading storage info:', error);
    }
}

async function loadDatabaseStatus() {
    try {
        const response = await fetch('/api/database/status');
        const data = await response.json();

        if (response.ok) {
            const dbStatus = document.getElementById('databaseStatus');
            const primaryDb = data.database_status.primary_database;
            const mongoAvailable = data.database_status.mongodb_available;

            dbStatus.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-muted">Primary Database</h6>
                        <p class="mb-0">
                            <span class="badge ${primaryDb === 'mongodb' ? 'bg-success' : 'bg-warning'}">${primaryDb.toUpperCase()}</span>
                        </p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted">MongoDB Status</h6>
                        <p class="mb-0">
                            <span class="badge ${mongoAvailable ? 'bg-success' : 'bg-danger'}">${mongoAvailable ? 'Connected' : 'Disconnected'}</span>
                        </p>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading database status:', error);
    }
}

function showError(message) {
    // Bootstrap toast notification
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${escapeHtml(message)}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

function showSuccess(message) {
    // Bootstrap toast notification
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${escapeHtml(message)}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// Utility function to escape HTML and prevent XSS
function escapeHtml(text) {
    if (typeof text !== 'string') return text;
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Utility function to safely get nested object properties
function safeGet(obj, path, defaultValue = '') {
    return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : defaultValue;
    }, obj);
}
</script>
{% endblock %}
