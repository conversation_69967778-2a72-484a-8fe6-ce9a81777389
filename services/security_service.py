import os
import time
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from cryptography.fernet import Fernet
import jwt
import re

class SecurityService:
    def __init__(self, cipher_suite=None):
        self.cipher_suite = cipher_suite
        self.rate_limits = {}  # IP -> {endpoint: [(timestamp, count), ...]}
        self.blocked_ips = set()
        self.suspicious_patterns = [
            r'bot|crawler|spider|scraper',
            r'curl|wget|python-requests',
            r'automated|script|headless'
        ]
        
    def is_suspicious_user_agent(self, user_agent: str) -> bool:
        """Check if user agent looks suspicious"""
        if not user_agent:
            return True
            
        user_agent_lower = user_agent.lower()
        
        for pattern in self.suspicious_patterns:
            if re.search(pattern, user_agent_lower):
                return True
                
        return False
    
    def check_rate_limit(self, ip_address: str, endpoint: str, limit: int = 10, window: int = 60) -> bool:
        """Check if IP is within rate limits for endpoint"""
        current_time = time.time()
        
        # Clean old entries
        if ip_address in self.rate_limits:
            self.rate_limits[ip_address] = {
                ep: [(ts, count) for ts, count in requests if current_time - ts < window]
                for ep, requests in self.rate_limits[ip_address].items()
            }
        
        # Initialize if needed
        if ip_address not in self.rate_limits:
            self.rate_limits[ip_address] = {}
        
        if endpoint not in self.rate_limits[ip_address]:
            self.rate_limits[ip_address][endpoint] = []
        
        # Count requests in current window
        requests = self.rate_limits[ip_address][endpoint]
        total_requests = sum(count for _, count in requests)
        
        if total_requests >= limit:
            # Add to blocked IPs if excessive
            if total_requests > limit * 2:
                self.blocked_ips.add(ip_address)
            return False
        
        # Add current request
        requests.append((current_time, 1))
        return True
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP is blocked"""
        return ip_address in self.blocked_ips
    
    def block_ip(self, ip_address: str):
        """Block an IP address"""
        self.blocked_ips.add(ip_address)
    
    def unblock_ip(self, ip_address: str):
        """Unblock an IP address"""
        self.blocked_ips.discard(ip_address)
    
    def encrypt_video_url(self, video_id: str, r2_key: str, expiration: int = 3600) -> Optional[str]:
        """Encrypt video URL with expiration"""
        if not self.cipher_suite:
            return None
            
        try:
            # Create payload with expiration
            payload = {
                'video_id': video_id,
                'r2_key': r2_key,
                'expires_at': time.time() + expiration
            }
            
            # Encrypt payload
            encrypted_data = self.cipher_suite.encrypt(str(payload).encode())
            return encrypted_data.decode()
            
        except Exception as e:
            print(f"Error encrypting video URL: {e}")
            return None
    
    def decrypt_video_url(self, encrypted_url: str) -> Optional[Dict]:
        """Decrypt video URL and check expiration"""
        if not self.cipher_suite:
            return None
            
        try:
            # Decrypt payload
            decrypted_data = self.cipher_suite.decrypt(encrypted_url.encode())
            payload = eval(decrypted_data.decode())  # Note: Use json in production
            
            # Check expiration
            if time.time() > payload['expires_at']:
                return None
                
            return payload
            
        except Exception as e:
            print(f"Error decrypting video URL: {e}")
            return None
    
    def generate_secure_token(self, data: Dict, expiration_hours: int = 1) -> str:
        """Generate secure JWT token"""
        payload = {
            **data,
            'exp': datetime.now() + timedelta(hours=expiration_hours),
            'iat': datetime.now()
        }
        
        secret_key = os.getenv('JWT_SECRET', 'default-secret')
        return jwt.encode(payload, secret_key, algorithm='HS256')
    
    def verify_secure_token(self, token: str) -> Optional[Dict]:
        """Verify JWT token"""
        try:
            secret_key = os.getenv('JWT_SECRET', 'default-secret')
            payload = jwt.decode(token, secret_key, algorithms=['HS256'])
            return payload
        except jwt.InvalidTokenError:
            return None
    
    def generate_video_access_signature(self, video_id: str, ip_address: str, timestamp: int) -> str:
        """Generate HMAC signature for video access"""
        secret_key = os.getenv('VIDEO_ENCRYPTION_KEY', 'default-key')
        message = f"{video_id}:{ip_address}:{timestamp}"
        signature = hmac.new(
            secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    def verify_video_access_signature(self, video_id: str, ip_address: str, timestamp: int, signature: str) -> bool:
        """Verify HMAC signature for video access"""
        # Check timestamp (allow 5 minute window)
        current_time = int(time.time())
        if abs(current_time - timestamp) > 300:  # 5 minutes
            return False
            
        expected_signature = self.generate_video_access_signature(video_id, ip_address, timestamp)
        return hmac.compare_digest(expected_signature, signature)
    
    def check_referrer_policy(self, referrer: str, allowed_domains: List[str] = None) -> bool:
        """Check if referrer is allowed"""
        if not referrer:
            return True  # Allow direct access
            
        if not allowed_domains:
            return True
            
        from urllib.parse import urlparse
        parsed_referrer = urlparse(referrer)
        referrer_domain = parsed_referrer.netloc.lower()
        
        for domain in allowed_domains:
            if referrer_domain == domain.lower() or referrer_domain.endswith(f'.{domain.lower()}'):
                return True
                
        return False
    
    def detect_automation(self, headers: Dict[str, str]) -> bool:
        """Detect automated requests"""
        user_agent = headers.get('user-agent', '').lower()
        
        # Check for automation indicators
        automation_indicators = [
            'headless',
            'phantom',
            'selenium',
            'webdriver',
            'automation',
            'bot',
            'crawler',
            'spider'
        ]
        
        for indicator in automation_indicators:
            if indicator in user_agent:
                return True
        
        # Check for missing common headers
        common_headers = ['accept', 'accept-language', 'accept-encoding']
        missing_headers = sum(1 for header in common_headers if header not in headers)
        
        if missing_headers >= 2:
            return True
            
        return False
    
    def get_client_fingerprint(self, headers: Dict[str, str], ip_address: str) -> str:
        """Generate client fingerprint"""
        fingerprint_data = [
            ip_address,
            headers.get('user-agent', ''),
            headers.get('accept-language', ''),
            headers.get('accept-encoding', ''),
            headers.get('accept', '')
        ]
        
        fingerprint_string = '|'.join(fingerprint_data)
        return hashlib.sha256(fingerprint_string.encode()).hexdigest()[:16]
    
    def log_security_event(self, event_type: str, ip_address: str, details: Dict = None):
        """Log security events"""
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'event_type': event_type,
            'ip_address': ip_address,
            'details': details or {}
        }
        
        # In production, send to logging service
        print(f"SECURITY EVENT: {log_entry}")
    
    def apply_anti_scraping_headers(self) -> Dict[str, str]:
        """Get anti-scraping HTTP headers"""
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    
    def get_security_stats(self) -> Dict[str, any]:
        """Get security statistics"""
        current_time = time.time()
        
        # Count active rate limits
        active_limits = 0
        for ip_data in self.rate_limits.values():
            for requests in ip_data.values():
                active_limits += len([r for r in requests if current_time - r[0] < 60])
        
        return {
            'blocked_ips': len(self.blocked_ips),
            'active_rate_limits': active_limits,
            'total_monitored_ips': len(self.rate_limits)
        }
