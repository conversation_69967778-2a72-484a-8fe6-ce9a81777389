"""
Local SQLite Database Service - Backup for Firebase
"""

import sqlite3
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import os
from .short_url_service import short_url_service

class LocalDBService:
    def __init__(self, db_path: str = "data/local_videos.db"):
        self.db_path = db_path
        
        # Create data directory if it doesn't exist
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self._init_database()
        print(f"✅ Local SQLite database initialized: {db_path}")
    
    def _init_database(self):
        """Initialize the SQLite database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS videos (
                    id TEXT PRIMARY KEY,
                    short_url TEXT UNIQUE,
                    title TEXT NOT NULL,
                    description TEXT,
                    filename TEXT,
                    file_size INTEGER DEFAULT 0,
                    duration INTEGER DEFAULT 0,
                    r2_key TEXT,
                    views INTEGER DEFAULT 0,
                    unique_views INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    status TEXT DEFAULT 'active',
                    analytics TEXT DEFAULT '{}'
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS view_sessions (
                    id TEXT PRIMARY KEY,
                    video_id TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id)
                )
            ''')
            
            conn.commit()
    
    def create_video(self, video_data: Dict[str, Any]) -> str:
        """Create a new video record in local database"""
        video_id = str(uuid.uuid4())

        # Generate short URL for clean links
        title = video_data.get('title', '')

        # Get existing short URLs to avoid conflicts
        existing_short_urls = set()
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('SELECT short_url FROM videos WHERE short_url IS NOT NULL')
                existing_short_urls = {row[0] for row in cursor.fetchall()}
        except:
            pass  # If query fails, continue with empty set

        short_url = short_url_service.generate_unique_short_url(video_id, title, existing_short_urls)

        # Set default expiration to 1 month
        expiration_date = datetime.now() + timedelta(days=30)

        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT INTO videos (
                    id, short_url, title, description, filename, file_size,
                    duration, r2_key, views, unique_views,
                    created_at, expires_at, status, analytics
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                video_id,
                short_url,
                title,
                video_data.get('description', ''),
                video_data.get('filename', ''),
                video_data.get('file_size', 0),
                video_data.get('duration', 0),
                video_data.get('r2_key', ''),
                0,  # views
                0,  # unique_views
                datetime.now().isoformat(),
                expiration_date.isoformat(),
                'active',
                json.dumps({
                    'total_views': 0,
                    'unique_ips': [],
                    'daily_views': {},
                    'referrers': {},
                    'user_agents': {},
                    'countries': {}
                })
            ))
            conn.commit()

        print(f"✅ Video created in local database: {video_id} (short: {short_url})")
        return video_id
    
    def get_video(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get video by ID from local database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('SELECT * FROM videos WHERE id = ?', (video_id,))
            row = cursor.fetchone()

            if row:
                return dict(row)
            return None

    def get_video_by_short_url(self, short_url: str) -> Optional[Dict[str, Any]]:
        """Get video by short URL from local database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('SELECT * FROM videos WHERE short_url = ?', (short_url,))
            row = cursor.fetchone()

            if row:
                return dict(row)
            return None
    
    def get_recent_videos(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent videos from local database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM videos 
                WHERE status = 'active' 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            
            videos = []
            for row in cursor.fetchall():
                video_data = dict(row)
                # Remove sensitive data for public API
                video_data.pop('r2_key', None)
                videos.append(video_data)
            
            print(f"✅ Retrieved {len(videos)} videos from local database")
            return videos
    
    def get_admin_videos(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get videos for admin management from local database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM videos 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
            
            videos = []
            for row in cursor.fetchall():
                video_data = dict(row)
                videos.append(video_data)
            
            print(f"✅ Retrieved {len(videos)} admin videos from local database")
            return videos
    
    def get_analytics_data(self) -> Dict[str, Any]:
        """Get analytics data from local database"""
        with sqlite3.connect(self.db_path) as conn:
            # Get total counts
            cursor = conn.execute('SELECT COUNT(*) as total_videos FROM videos WHERE status = "active"')
            total_videos = cursor.fetchone()[0]
            
            cursor = conn.execute('SELECT SUM(views) as total_views FROM videos')
            total_views = cursor.fetchone()[0] or 0
            
            cursor = conn.execute('SELECT SUM(unique_views) as total_unique_views FROM videos')
            total_unique_views = cursor.fetchone()[0] or 0
            
            # Get top videos
            cursor = conn.execute('''
                SELECT id, title, views, created_at 
                FROM videos 
                WHERE status = "active" 
                ORDER BY views DESC 
                LIMIT 10
            ''')
            
            top_videos = []
            for row in cursor.fetchall():
                top_videos.append({
                    'id': row[0],
                    'title': row[1],
                    'views': row[2],
                    'created_at': row[3]
                })
            
            return {
                'total_videos': total_videos,
                'total_views': total_views,
                'total_unique_views': total_unique_views,
                'daily_stats': {},
                'top_videos': top_videos
            }
    
    def delete_video(self, video_id: str) -> bool:
        """Delete video from local database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('DELETE FROM videos WHERE id = ?', (video_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def increment_views(self, video_id: str, ip_address: str = None) -> bool:
        """Increment view count for a video"""
        with sqlite3.connect(self.db_path) as conn:
            # Update view count
            conn.execute('UPDATE videos SET views = views + 1 WHERE id = ?', (video_id,))
            
            # Record view session if IP provided
            if ip_address:
                session_id = str(uuid.uuid4())
                conn.execute('''
                    INSERT INTO view_sessions (id, video_id, ip_address, created_at)
                    VALUES (?, ?, ?, ?)
                ''', (session_id, video_id, ip_address, datetime.now().isoformat()))
            
            conn.commit()
            return True
