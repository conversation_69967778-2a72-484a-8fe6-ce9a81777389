"""
Service Registry - Global access to services
"""

# Global service instances
firebase_service = None
r2_service = None
security_service = None

def set_firebase_service(service):
    """Set the Firebase service instance"""
    global firebase_service
    firebase_service = service

def set_database_service(service):
    """Set the Firebase service instance (alias)"""
    global firebase_service
    firebase_service = service

def set_r2_service(service):
    """Set the R2 service instance"""
    global r2_service
    r2_service = service

def set_security_service(service):
    """Set the Security service instance"""
    global security_service
    security_service = service

def get_firebase_service():
    """Get the Firebase service instance"""
    return firebase_service

def get_database_service():
    """Get the Firebase service instance (alias)"""
    return firebase_service

def get_r2_service():
    """Get the R2 service instance"""
    return r2_service

def get_security_service():
    """Get the Security service instance"""
    return security_service
