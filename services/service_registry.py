"""
Service Registry - Global access to services
"""

# Global service instances
database_service = None
r2_service = None
security_service = None

def set_firebase_service(service):
    """Set the database service instance (legacy name for compatibility)"""
    global database_service
    database_service = service

def set_database_service(service):
    """Set the database service instance"""
    global database_service
    database_service = service

def set_r2_service(service):
    """Set the R2 service instance"""
    global r2_service
    r2_service = service

def set_security_service(service):
    """Set the Security service instance"""
    global security_service
    security_service = service

def get_firebase_service():
    """Get the database service instance (legacy name for compatibility)"""
    return database_service

def get_database_service():
    """Get the database service instance"""
    return database_service

def get_r2_service():
    """Get the R2 service instance"""
    return r2_service

def get_security_service():
    """Get the Security service instance"""
    return security_service
