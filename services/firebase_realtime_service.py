"""
Firebase Realtime Database Service
"""

import os
import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import firebase_admin
from firebase_admin import credentials, db
from .short_url_service import short_url_service

class FirebaseRealtimeService:
    def __init__(self, service_account_path: str = None, database_url: str = None):
        self.service_account_path = service_account_path or os.getenv('FIREBASE_SERVICE_ACCOUNT')
        self.database_url = database_url or os.getenv('FIREBASE_DATABASE_URL')
        self.app = None
        self.db_ref = None
        
        # Initialize Firebase
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            # Check if Firebase app already exists
            try:
                self.app = firebase_admin.get_app()
                print("✅ Using existing Firebase app")
            except ValueError:
                # Initialize new Firebase app
                if self.service_account_path and os.path.exists(self.service_account_path):
                    cred = credentials.Certificate(self.service_account_path)
                    self.app = firebase_admin.initialize_app(cred, {
                        'databaseURL': self.database_url
                    })
                    print("✅ Firebase initialized with service account")
                else:
                    # Try to use environment variable for service account
                    firebase_config = os.getenv('FIREBASE_CONFIG')
                    if firebase_config:
                        config_dict = json.loads(firebase_config)
                        cred = credentials.Certificate(config_dict)
                        self.app = firebase_admin.initialize_app(cred, {
                            'databaseURL': self.database_url
                        })
                        print("✅ Firebase initialized with environment config")
                    else:
                        raise Exception("No Firebase credentials found")
            
            # Get database reference
            self.db_ref = db.reference('/', app=self.app)
            
            # Test connection
            self.db_ref.child('test').set({'timestamp': datetime.now().isoformat()})
            print("✅ Firebase Realtime Database connected successfully")
            
        except Exception as e:
            print(f"❌ Firebase initialization failed: {e}")
            print("💡 Please check your Firebase configuration")
            self.app = None
            self.db_ref = None
    
    def is_connected(self) -> bool:
        """Check if Firebase is connected"""
        try:
            if self.db_ref:
                # Try a simple read operation
                self.db_ref.child('test').get()
                return True
        except Exception as e:
            print(f"⚠️ Firebase connection test failed: {e}")
        return False
    
    def create_video(self, video_data: Dict[str, Any]) -> str:
        """Create a new video record in Firebase Realtime Database"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, cannot create video")
                return None
                
            video_id = str(uuid.uuid4())
            title = video_data.get('title', 'Untitled')
            
            # Get existing short URLs to avoid duplicates
            existing_short_urls = set()
            try:
                videos_ref = self.db_ref.child('videos')
                existing_videos = videos_ref.get() or {}
                existing_short_urls = {video.get('short_url') for video in existing_videos.values() if video.get('short_url')}
            except Exception as e:
                print(f"⚠️ Could not fetch existing short URLs: {e}")

            short_url = short_url_service.generate_unique_short_url(video_id, title, existing_short_urls)

            # Set default expiration to 1 month
            expiration_date = datetime.now() + timedelta(days=30)

            video_doc = {
                'id': video_id,
                'short_url': short_url,
                'title': title,
                'description': video_data.get('description', ''),
                'filename': video_data.get('filename', ''),
                'file_size': video_data.get('file_size', 0),
                'duration': video_data.get('duration', 0),
                'r2_key': video_data.get('r2_key', ''),
                'views': 0,
                'unique_views': 0,
                'created_at': datetime.now().isoformat(),
                'expires_at': expiration_date.isoformat(),
                'status': 'active',
                'analytics': {
                    'total_views': 0,
                    'unique_ips': [],
                    'daily_views': {},
                    'referrers': {},
                    'user_agents': {},
                    'countries': {}
                }
            }
            
            # Insert into Firebase Realtime Database
            videos_ref = self.db_ref.child('videos')
            videos_ref.child(video_id).set(video_doc)

            # Also store the short URL mapping for efficient lookup
            short_urls_ref = self.db_ref.child('short_urls')
            short_urls_ref.child(short_url).set(video_id)

            print(f"✅ Video created in Firebase: {video_id} with short URL: {short_url}")
            return video_id
                
        except Exception as e:
            print(f"❌ Error creating video in Firebase: {e}")
            raise e
    
    def get_video(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get video by ID from Firebase"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, cannot get video")
                return None
                
            video_ref = self.db_ref.child('videos').child(video_id)
            video = video_ref.get()
            
            if video:
                return video
            return None

        except Exception as e:
            print(f"❌ Error getting video {video_id}: {e}")
            return None

    def get_video_by_short_url(self, short_url: str) -> Optional[Dict[str, Any]]:
        """Get video by short URL from Firebase"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, cannot get video")
                return None

            # First try to get from short_urls mapping (more efficient)
            short_url_ref = self.db_ref.child('short_urls').child(short_url)
            video_id = short_url_ref.get()

            if video_id:
                # Get the actual video data
                video_ref = self.db_ref.child('videos').child(video_id)
                video_data = video_ref.get()
                if video_data:
                    video_data['id'] = video_id
                    return video_data

            # Fallback: search through all videos (less efficient but works without indexing)
            print(f"🔍 Searching for short URL {short_url} in all videos...")
            videos_ref = self.db_ref.child('videos')
            all_videos = videos_ref.get()

            if all_videos:
                for video_id, video_data in all_videos.items():
                    if video_data and video_data.get('short_url') == short_url:
                        video_data['id'] = video_id
                        print(f"✅ Found video {video_id} with short URL {short_url}")
                        return video_data

            print(f"❌ No video found with short URL: {short_url}")
            return None

        except Exception as e:
            print(f"❌ Error getting video by short URL {short_url}: {e}")
            return None
    
    def get_recent_videos(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent videos from Firebase"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, returning empty video list")
                return []
                
            videos_ref = self.db_ref.child('videos')
            videos = videos_ref.order_by_child('created_at').limit_to_last(limit).get()
            
            if videos:
                # Convert to list and reverse to get newest first
                video_list = []
                for video_id, video_data in videos.items():
                    if video_data.get('status') == 'active':
                        video_list.append(video_data)
                
                # Sort by created_at descending
                video_list.sort(key=lambda x: x.get('created_at', ''), reverse=True)
                return video_list
            
            return []

        except Exception as e:
            print(f"❌ Error getting recent videos: {e}")
            return []
    
    def get_admin_videos(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get videos for admin dashboard"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, returning empty video list")
                return []
                
            videos_ref = self.db_ref.child('videos')
            videos = videos_ref.order_by_child('created_at').limit_to_last(limit).get()
            
            if videos:
                video_list = []
                for video_id, video_data in videos.items():
                    video_list.append(video_data)
                
                # Sort by created_at descending
                video_list.sort(key=lambda x: x.get('created_at', ''), reverse=True)
                return video_list
            
            return []

        except Exception as e:
            print(f"❌ Error getting admin videos: {e}")
            return []
    
    def delete_video(self, video_id: str) -> bool:
        """Delete video from Firebase"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, cannot delete video")
                return False
                
            video_ref = self.db_ref.child('videos').child(video_id)
            video_ref.delete()
            
            print(f"✅ Video {video_id} deleted from Firebase")
            return True
                
        except Exception as e:
            print(f"❌ Error deleting video {video_id}: {e}")
            return False
    
    def increment_views(self, video_id: str, ip_address: str = None) -> bool:
        """Increment view count for a video"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, cannot increment views")
                return False
                
            video_ref = self.db_ref.child('videos').child(video_id)
            
            # Get current views and increment
            current_views = video_ref.child('views').get() or 0
            video_ref.child('views').set(current_views + 1)
            
            # Record analytics if needed
            if ip_address:
                analytics_ref = self.db_ref.child('analytics').child(video_id)
                analytics_ref.push({
                    'ip_address': ip_address,
                    'timestamp': datetime.now().isoformat(),
                    'type': 'view'
                })
            
            return True
            
        except Exception as e:
            print(f"❌ Error incrementing views for {video_id}: {e}")
            return False

    async def create_session(self, video_id: str, ip_address: str, user_agent: str) -> str:
        """Create a viewing session"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, cannot create session")
                return str(uuid.uuid4())  # Return fallback session ID

            session_id = str(uuid.uuid4())
            session_data = {
                'video_id': video_id,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(minutes=20)).isoformat()
            }

            # Store session in Firebase
            sessions_ref = self.db_ref.child('sessions')
            sessions_ref.child(session_id).set(session_data)

            print(f"✅ Session created: {session_id}")
            return session_id

        except Exception as e:
            print(f"❌ Error creating session: {e}")
            return str(uuid.uuid4())  # Return a fallback session ID

    async def check_view_session(self, video_id: str, ip_address: str) -> bool:
        """Check if this IP should count as a new view (120min limit)"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, defaulting to count view")
                return True

            # Check for recent view sessions from this IP for this video
            cutoff_time = datetime.now() - timedelta(minutes=120)

            sessions_ref = self.db_ref.child('sessions')
            sessions = sessions_ref.order_by_child('video_id').equal_to(video_id).get()

            if sessions:
                for session_id, session_data in sessions.items():
                    if (session_data.get('ip_address') == ip_address and
                        session_data.get('created_at') > cutoff_time.isoformat()):
                        print(f"🔍 View session check for {video_id} from {ip_address}: SKIP")
                        return False

            # Return True if no recent session (should count view)
            print(f"🔍 View session check for {video_id} from {ip_address}: COUNT")
            return True

        except Exception as e:
            print(f"❌ Error checking view session: {e}")
            return True  # Default to counting the view

    async def update_video_views(self, video_id: str, ip_address: str, user_agent: str, referrer: str = '') -> bool:
        """Update video view count and analytics"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, cannot update views")
                return False

            video_ref = self.db_ref.child('videos').child(video_id)

            # Get current video data
            video_data = video_ref.get()
            if not video_data:
                return False

            # Update view count
            current_views = video_data.get('views', 0)
            video_ref.child('views').set(current_views + 1)
            video_ref.child('last_viewed').set(datetime.now().isoformat())

            # Update analytics
            analytics_ref = video_ref.child('analytics')
            today = datetime.now().strftime('%Y-%m-%d')

            # Update daily views
            daily_views = video_data.get('analytics', {}).get('daily_views', {})
            daily_views[today] = daily_views.get(today, 0) + 1
            analytics_ref.child('daily_views').set(daily_views)

            # Update total views
            total_views = video_data.get('analytics', {}).get('total_views', 0)
            analytics_ref.child('total_views').set(total_views + 1)

            # Add IP to unique IPs if not already present
            unique_ips = video_data.get('analytics', {}).get('unique_ips', [])
            if ip_address not in unique_ips:
                unique_ips.append(ip_address)
                analytics_ref.child('unique_ips').set(unique_ips)

                # Update unique views count
                current_unique_views = video_data.get('unique_views', 0)
                video_ref.child('unique_views').set(current_unique_views + 1)

            # Update referrers
            if referrer:
                referrers = video_data.get('analytics', {}).get('referrers', {})
                referrers[referrer] = referrers.get(referrer, 0) + 1
                analytics_ref.child('referrers').set(referrers)

            # Update user agents
            if user_agent:
                user_agents = video_data.get('analytics', {}).get('user_agents', {})
                ua_key = user_agent[:50]  # Truncate for storage efficiency
                user_agents[ua_key] = user_agents.get(ua_key, 0) + 1
                analytics_ref.child('user_agents').set(user_agents)

            print(f"✅ Updated views for video {video_id}")
            return True

        except Exception as e:
            print(f"❌ Error updating video views: {e}")
            return False

    def get_analytics_data_sync(self) -> Dict[str, Any]:
        """Get comprehensive analytics data from Firebase"""
        try:
            if not self.db_ref:
                print("❌ Firebase not connected, returning empty analytics")
                return {
                    'total_videos': 0,
                    'total_views': 0,
                    'unique_viewers': 0,
                    'videos_today': 0,
                    'views_today': 0,
                    'top_videos': [],
                    'recent_activity': [],
                    'daily_stats': {}
                }

            videos_ref = self.db_ref.child('videos')
            videos = videos_ref.get() or {}

            # Calculate totals
            total_videos = len([v for v in videos.values() if v.get('status') == 'active'])
            total_views = sum(v.get('views', 0) for v in videos.values())

            # Calculate unique viewers
            all_unique_ips = set()
            for video in videos.values():
                unique_ips = video.get('analytics', {}).get('unique_ips', [])
                all_unique_ips.update(unique_ips)
            unique_viewers = len(all_unique_ips)

            # Today's stats
            today = datetime.now().strftime('%Y-%m-%d')
            videos_today = len([v for v in videos.values() if v.get('created_at', '').startswith(today)])
            views_today = sum(
                v.get('analytics', {}).get('daily_views', {}).get(today, 0)
                for v in videos.values()
            )

            # Top videos
            top_videos = sorted(
                [v for v in videos.values() if v.get('status') == 'active'],
                key=lambda x: x.get('views', 0),
                reverse=True
            )[:10]

            # Recent activity (last 10 videos)
            recent_activity = sorted(
                [v for v in videos.values() if v.get('status') == 'active'],
                key=lambda x: x.get('created_at', ''),
                reverse=True
            )[:10]

            return {
                'total_videos': total_videos,
                'total_views': total_views,
                'unique_viewers': unique_viewers,
                'videos_today': videos_today,
                'views_today': views_today,
                'top_videos': top_videos,
                'recent_activity': recent_activity,
                'daily_stats': {}  # Can be expanded later
            }

        except Exception as e:
            print(f"❌ Error getting analytics data: {e}")
            return {
                'total_videos': 0,
                'total_views': 0,
                'unique_viewers': 0,
                'videos_today': 0,
                'views_today': 0,
                'top_videos': [],
                'recent_activity': [],
                'daily_stats': {}
            }

    def get_database_status(self) -> Dict[str, Any]:
        """Get Firebase database status"""
        try:
            if not self.db_ref:
                return {
                    'firebase_connected': False,
                    'database_url': self.database_url,
                    'error': 'Firebase not initialized',
                    'collections': {}
                }

            # Test connection and get basic stats
            test_result = self.db_ref.child('test').get()

            # Get collection counts
            videos_count = 0
            sessions_count = 0
            analytics_count = 0

            try:
                videos = self.db_ref.child('videos').get()
                videos_count = len(videos) if videos else 0
            except:
                pass

            try:
                sessions = self.db_ref.child('sessions').get()
                sessions_count = len(sessions) if sessions else 0
            except:
                pass

            try:
                analytics = self.db_ref.child('analytics').get()
                analytics_count = len(analytics) if analytics else 0
            except:
                pass

            return {
                'firebase_connected': True,
                'database_url': self.database_url,
                'collections': {
                    'videos': videos_count,
                    'sessions': sessions_count,
                    'analytics': analytics_count
                },
                'firebase_status': {
                    'test_connection': test_result is not None,
                    'last_test': datetime.now().isoformat()
                }
            }

        except Exception as e:
            print(f"❌ Error getting database status: {e}")
            return {
                'firebase_connected': False,
                'database_url': self.database_url,
                'error': str(e),
                'collections': {}
            }
