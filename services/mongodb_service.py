"""
MongoDB Database Service
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pymongo import MongoClient, DESCENDING
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
import json
from .short_url_service import short_url_service

class MongoDBService:
    def __init__(self, connection_string: str = None):
        self.connection_string = connection_string or os.getenv('mongo_db')
        self.client = None
        self.db = None
        self.videos_collection = None
        self.analytics_collection = None
        
        # Initialize connection
        self._connect()
    
    def _connect(self):
        """Connect to MongoDB"""
        try:
            if not self.connection_string:
                raise ValueError("MongoDB connection string not provided")
            
            # Parse database name from connection string
            if '/' in self.connection_string and not self.connection_string.endswith('/'):
                # Extract database name from URL
                parts = self.connection_string.split('/')
                if len(parts) > 3:
                    db_name = parts[-1].split('?')[0]
                    if not db_name or db_name == '':
                        db_name = 'video_platform'
                else:
                    db_name = 'video_platform'
            else:
                db_name = 'video_platform'

            print(f"🔗 Connecting to MongoDB cluster...")
            
            # Create client with timeout and SSL configuration
            self.client = MongoClient(
                self.connection_string,
                serverSelectionTimeoutMS=10000,  # 10 second timeout
                connectTimeoutMS=10000,
                socketTimeoutMS=10000,
                tls=True,
                tlsAllowInvalidCertificates=True,  # Allow invalid certificates
                retryWrites=True
            )
            
            # Test connection
            self.client.admin.command('ping')
            
            # Get database and collections
            self.db = self.client[db_name]
            self.videos_collection = self.db.videos
            self.analytics_collection = self.db.analytics
            
            # Create indexes for better performance
            self._create_indexes()
            
            print("✅ MongoDB connected successfully")
            return True
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            print(f"❌ MongoDB connection failed: {e}")
            print("💡 Please check your MongoDB connection string and network connectivity")
            self.client = None
            self.db = None
            return False
        except Exception as e:
            print(f"❌ MongoDB initialization error: {e}")
            print("💡 Please verify your MongoDB credentials and connection string")
            self.client = None
            self.db = None
            return False
    
    def _create_indexes(self):
        """Create database indexes for better performance"""
        try:
            # Index on created_at for sorting
            self.videos_collection.create_index([("created_at", DESCENDING)])
            
            # Index on status for filtering
            self.videos_collection.create_index("status")
            
            # Index on video_id for analytics
            self.analytics_collection.create_index("video_id")
            
            print("✅ MongoDB indexes created")
        except Exception as e:
            print(f"⚠️ Failed to create indexes: {e}")
    
    def is_connected(self) -> bool:
        """Check if MongoDB is connected"""
        try:
            if self.client:
                self.client.admin.command('ping')
                return True
        except:
            pass
        return False
    
    def create_video(self, video_data: Dict[str, Any]) -> str:
        """Create a new video document in MongoDB"""
        try:
            if not self.client or not self.db:
                print("❌ MongoDB not connected, cannot create video")
                return None

            video_id = str(uuid.uuid4())

            # Generate short URL for clean links
            title = video_data.get('title', '')

            # Get existing short URLs to avoid conflicts
            existing_short_urls = set()
            try:
                existing_docs = self.videos_collection.find({}, {'short_url': 1})
                existing_short_urls = {doc.get('short_url') for doc in existing_docs if doc.get('short_url')}
            except:
                pass  # If query fails, continue with empty set

            short_url = short_url_service.generate_unique_short_url(video_id, title, existing_short_urls)

            # Set default expiration to 1 month
            expiration_date = datetime.now() + timedelta(days=30)

            video_doc = {
                '_id': video_id,
                'id': video_id,
                'short_url': short_url,
                'title': title,
                'description': video_data.get('description', ''),
                'filename': video_data.get('filename', ''),
                'file_size': video_data.get('file_size', 0),
                'duration': video_data.get('duration', 0),
                'r2_key': video_data.get('r2_key', ''),
                'views': 0,
                'unique_views': 0,
                'created_at': datetime.now(),
                'expires_at': expiration_date,
                'status': 'active',
                'analytics': {
                    'total_views': 0,
                    'unique_ips': [],
                    'daily_views': {},
                    'referrers': {},
                    'user_agents': {},
                    'countries': {}
                }
            }
            
            # Insert into MongoDB
            result = self.videos_collection.insert_one(video_doc)
            
            if result.inserted_id:
                print(f"✅ Video created in MongoDB: {video_id}")
                return video_id
            else:
                raise Exception("Failed to insert video document")
                
        except Exception as e:
            print(f"❌ Error creating video in MongoDB: {e}")
            raise e
    
    def get_video(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get video by ID from MongoDB"""
        try:
            if not self.client or not self.db:
                print("❌ MongoDB not connected, cannot get video")
                return None

            video = self.videos_collection.find_one({'_id': video_id})

            if video:
                # Convert ObjectId and datetime to strings for JSON serialization
                video = self._serialize_document(video)
                return video
            return None

        except Exception as e:
            print(f"❌ Error getting video {video_id}: {e}")
            return None

    def get_video_by_short_url(self, short_url: str) -> Optional[Dict[str, Any]]:
        """Get video by short URL from MongoDB"""
        try:
            video = self.videos_collection.find_one({'short_url': short_url})

            if video:
                # Convert ObjectId and datetime to strings for JSON serialization
                video = self._serialize_document(video)
                return video
            return None

        except Exception as e:
            print(f"❌ Error getting video by short URL {short_url}: {e}")
            return None
    
    def get_recent_videos(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent videos from MongoDB"""
        try:
            if not self.client or not self.db:
                print("❌ MongoDB not connected, returning empty video list")
                return []

            cursor = self.videos_collection.find(
                {'status': 'active'}
            ).sort('created_at', DESCENDING).limit(limit)
            
            videos = []
            for doc in cursor:
                video_data = self._serialize_document(doc)
                # Remove sensitive data for public API
                video_data.pop('r2_key', None)
                videos.append(video_data)
            
            print(f"✅ Retrieved {len(videos)} videos from MongoDB")
            return videos
            
        except Exception as e:
            print(f"❌ Error getting recent videos: {e}")
            return []
    
    def get_admin_videos(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get videos for admin management from MongoDB"""
        try:
            cursor = self.videos_collection.find().sort('created_at', DESCENDING).limit(limit)
            
            videos = []
            for doc in cursor:
                video_data = self._serialize_document(doc)
                videos.append(video_data)
            
            print(f"✅ Retrieved {len(videos)} admin videos from MongoDB")
            return videos
            
        except Exception as e:
            print(f"❌ Error getting admin videos: {e}")
            return []
    
    def get_analytics_data_sync(self) -> Dict[str, Any]:
        """Get comprehensive analytics data from MongoDB"""
        try:
            # Get total counts using aggregation
            pipeline = [
                {'$match': {'status': 'active'}},
                {'$group': {
                    '_id': None,
                    'total_videos': {'$sum': 1},
                    'total_views': {'$sum': '$views'},
                    'total_unique_views': {'$sum': '$unique_views'}
                }}
            ]
            
            result = list(self.videos_collection.aggregate(pipeline))
            
            if result:
                stats = result[0]
                total_videos = stats.get('total_videos', 0)
                total_views = stats.get('total_views', 0)
                total_unique_views = stats.get('total_unique_views', 0)
            else:
                total_videos = total_views = total_unique_views = 0
            
            # Get top videos
            top_videos_cursor = self.videos_collection.find(
                {'status': 'active'},
                {'id': 1, 'title': 1, 'views': 1, 'created_at': 1}
            ).sort('views', DESCENDING).limit(10)
            
            top_videos = []
            for doc in top_videos_cursor:
                video_data = self._serialize_document(doc)
                top_videos.append(video_data)
            
            print(f"✅ Analytics: {total_videos} videos, {total_views} total views")
            
            return {
                'total_videos': total_videos,
                'total_views': total_views,
                'total_unique_views': total_unique_views,
                'daily_stats': {},  # Can be implemented later
                'top_videos': top_videos
            }
            
        except Exception as e:
            print(f"❌ Error getting analytics data: {e}")
            return {
                'total_videos': 0,
                'total_views': 0,
                'total_unique_views': 0,
                'daily_stats': {},
                'top_videos': []
            }
    
    def delete_video(self, video_id: str) -> bool:
        """Delete video from MongoDB"""
        try:
            result = self.videos_collection.delete_one({'_id': video_id})
            
            if result.deleted_count > 0:
                print(f"✅ Video {video_id} deleted from MongoDB")
                return True
            else:
                print(f"⚠️ Video {video_id} not found in MongoDB")
                return False
                
        except Exception as e:
            print(f"❌ Error deleting video {video_id}: {e}")
            return False
    
    def increment_views(self, video_id: str, ip_address: str = None) -> bool:
        """Increment view count for a video"""
        try:
            # Update view count
            result = self.videos_collection.update_one(
                {'_id': video_id},
                {'$inc': {'views': 1}}
            )
            
            # Record analytics if needed
            if ip_address and result.modified_count > 0:
                analytics_doc = {
                    'video_id': video_id,
                    'ip_address': ip_address,
                    'timestamp': datetime.now(),
                    'type': 'view'
                }
                self.analytics_collection.insert_one(analytics_doc)
            
            return result.modified_count > 0
            
        except Exception as e:
            print(f"❌ Error incrementing views for {video_id}: {e}")
            return False

    async def create_session(self, video_id: str, ip_address: str, user_agent: str) -> str:
        """Create a viewing session"""
        try:
            session_id = str(uuid.uuid4())
            session_doc = {
                '_id': session_id,
                'video_id': video_id,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'created_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(minutes=20)
            }

            # Store session in sessions collection
            sessions_collection = self.db.sessions
            sessions_collection.insert_one(session_doc)

            print(f"✅ Session created: {session_id}")
            return session_id

        except Exception as e:
            print(f"❌ Error creating session: {e}")
            return str(uuid.uuid4())  # Return a fallback session ID

    async def check_view_session(self, video_id: str, ip_address: str) -> bool:
        """Check if this IP should count as a new view (120min limit)"""
        try:
            # Check for recent view sessions from this IP for this video
            cutoff_time = datetime.now() - timedelta(minutes=120)

            sessions_collection = self.db.sessions
            recent_session = sessions_collection.find_one({
                'video_id': video_id,
                'ip_address': ip_address,
                'created_at': {'$gte': cutoff_time}
            })

            # Return True if no recent session (should count view)
            should_count = recent_session is None
            print(f"🔍 View session check for {video_id} from {ip_address}: {'COUNT' if should_count else 'SKIP'}")
            return should_count

        except Exception as e:
            print(f"❌ Error checking view session: {e}")
            return True  # Default to counting the view

    async def update_video_views(self, video_id: str, ip_address: str, user_agent: str, referrer: str = '') -> bool:
        """Update video view count and analytics"""
        try:
            # Update view count
            result = self.videos_collection.update_one(
                {'_id': video_id},
                {
                    '$inc': {'views': 1},
                    '$set': {'last_viewed': datetime.now()}
                }
            )

            # Update analytics
            today = datetime.now().strftime('%Y-%m-%d')
            analytics_update = {
                f'analytics.daily_views.{today}': 1,
                'analytics.total_views': 1
            }

            if referrer:
                analytics_update[f'analytics.referrers.{referrer}'] = 1

            if user_agent:
                analytics_update[f'analytics.user_agents.{user_agent[:50]}'] = 1

            # Add IP to unique IPs if not already present
            self.videos_collection.update_one(
                {'_id': video_id, 'analytics.unique_ips': {'$ne': ip_address}},
                {
                    '$push': {'analytics.unique_ips': ip_address},
                    '$inc': {'unique_views': 1}
                }
            )

            # Update analytics with increments
            self.videos_collection.update_one(
                {'_id': video_id},
                {'$inc': analytics_update}
            )

            print(f"✅ Updated views for video {video_id}")
            return result.modified_count > 0

        except Exception as e:
            print(f"❌ Error updating video views: {e}")
            return False

    def get_database_status(self) -> Dict[str, Any]:
        """Get MongoDB database status"""
        try:
            # Test connection
            self.client.admin.command('ping')

            # Get database stats
            db_stats = self.db.command('dbStats')

            # Get collection info
            collections = {}
            for collection_name in self.db.list_collection_names():
                collection = self.db[collection_name]
                count = collection.count_documents({})
                collections[collection_name] = count

            return {
                'mongodb_connected': True,
                'database_name': self.db.name,
                'connection_string': self.connection_string.split('@')[1] if '@' in self.connection_string else 'hidden',
                'collections': collections,
                'mongodb_status': {
                    'storage_size': db_stats.get('storageSize', 0),
                    'data_size': db_stats.get('dataSize', 0),
                    'index_size': db_stats.get('indexSize', 0),
                    'objects': db_stats.get('objects', 0)
                }
            }

        except Exception as e:
            print(f"❌ Error getting database status: {e}")
            return {
                'mongodb_connected': False,
                'error': str(e),
                'database_name': None,
                'connection_string': self.connection_string.split('@')[1] if '@' in self.connection_string else 'hidden',
                'collections': {}
            }
    
    def _serialize_document(self, doc: Dict[str, Any]) -> Dict[str, Any]:
        """Convert MongoDB document to JSON-serializable format"""
        if not doc:
            return doc
        
        # Convert datetime objects to ISO strings
        for key, value in doc.items():
            if isinstance(value, datetime):
                doc[key] = value.isoformat()
        
        return doc
    
    def get_database_status(self) -> Dict[str, Any]:
        """Get MongoDB database status"""
        return {
            'mongodb_connected': self.is_connected(),
            'database_name': self.db.name if self.db else None,
            'collections': {
                'videos': self.videos_collection.count_documents({}) if self.videos_collection else 0,
                'analytics': self.analytics_collection.count_documents({}) if self.analytics_collection else 0
            } if self.is_connected() else {},
            'connection_string': self.connection_string[:50] + "..." if self.connection_string else None
        }
