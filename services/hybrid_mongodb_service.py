"""
Hybrid MongoDB Service - Uses MongoDB when available, falls back to SQLite
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from .mongodb_service import MongoDBService
from .local_db_service import LocalDBService

class HybridMongoDBService:
    def __init__(self, mongodb_connection: str = None):
        # Initialize both services
        self.mongodb_service = None
        self.local_service = LocalDBService()
        self.mongodb_available = False
        
        # Try to initialize MongoDB
        if mongodb_connection:
            try:
                print("🍃 Attempting MongoDB connection...")
                self.mongodb_service = MongoDBService(mongodb_connection)
                self.mongodb_available = self.mongodb_service.is_connected()
                
                if self.mongodb_available:
                    print("✅ Hybrid DB: Using MongoDB as primary database")
                else:
                    print("⚠️ Hybrid DB: MongoDB unavailable, using local SQLite")
            except Exception as e:
                print(f"⚠️ MongoDB initialization failed: {e}")
                print("⚠️ Hybrid DB: Using local SQLite as primary database")
                self.mongodb_available = False
        else:
            print("⚠️ Hybrid DB: No MongoDB connection string, using local SQLite only")
    
    def create_video(self, video_data: Dict[str, Any]) -> str:
        """Create video in both databases for redundancy"""
        # Always create in local database first
        video_id = self.local_service.create_video(video_data)
        
        # Try to create in MongoDB if available
        if self.mongodb_available and self.mongodb_service:
            try:
                # Use the same video_id for consistency
                mongodb_data = video_data.copy()
                mongodb_data['id'] = video_id
                self.mongodb_service.create_video(mongodb_data)
                print(f"✅ Video {video_id} created in both databases")
            except Exception as e:
                print(f"⚠️ Failed to create in MongoDB, using local only: {e}")
                self.mongodb_available = False
        
        return video_id
    
    def get_video(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get video from MongoDB first, fallback to local"""
        if self.mongodb_available and self.mongodb_service:
            try:
                video = self.mongodb_service.get_video(video_id)
                if video:
                    return video
            except Exception as e:
                print(f"⚠️ MongoDB get_video failed: {e}")
                self.mongodb_available = False

        # Fallback to local database
        return self.local_service.get_video(video_id)

    def get_video_by_short_url(self, short_url: str) -> Optional[Dict[str, Any]]:
        """Get video by short URL from MongoDB first, fallback to local"""
        if self.mongodb_available and self.mongodb_service:
            try:
                video = self.mongodb_service.get_video_by_short_url(short_url)
                if video:
                    return video
            except Exception as e:
                print(f"⚠️ MongoDB get_video_by_short_url failed: {e}")
                self.mongodb_available = False

        # Fallback to local database
        return self.local_service.get_video_by_short_url(short_url)
    
    def get_recent_videos(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent videos from best available source"""
        if self.mongodb_available and self.mongodb_service:
            try:
                videos = self.mongodb_service.get_recent_videos(limit)
                if videos:
                    return videos
            except Exception as e:
                print(f"⚠️ MongoDB get_recent_videos failed: {e}")
                self.mongodb_available = False
        
        # Use local database
        local_videos = self.local_service.get_recent_videos(limit)
        
        # If no local videos, return informative message
        if not local_videos:
            return [{
                'id': 'no_videos',
                'title': '🎬 No Videos Available',
                'description': 'No videos have been uploaded yet. Use the admin panel to upload your first video!',
                'views': 0,
                'unique_views': 0,
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(days=30)).isoformat(),
                'file_size': 0,
                'status': 'empty'
            }]
        
        return local_videos
    
    def get_admin_videos(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get admin videos from best available source"""
        if self.mongodb_available and self.mongodb_service:
            try:
                videos = self.mongodb_service.get_admin_videos(limit)
                if videos:
                    return videos
            except Exception as e:
                print(f"⚠️ MongoDB get_admin_videos failed: {e}")
                self.mongodb_available = False
        
        # Use local database
        local_videos = self.local_service.get_admin_videos(limit)
        
        # If no local videos, return helpful message
        if not local_videos:
            return [{
                'id': 'admin_empty',
                'title': '📁 No Videos in Database',
                'description': 'Local database is empty. Upload videos using the form above. Videos will be stored locally and synced to MongoDB when available.',
                'views': 0,
                'unique_views': 0,
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(days=30)).isoformat(),
                'file_size': 0,
                'status': 'empty',
                'r2_key': 'none'
            }]
        
        return local_videos
    
    def get_analytics_data_sync(self) -> Dict[str, Any]:
        """Get analytics from best available source"""
        if self.mongodb_available and self.mongodb_service:
            try:
                analytics = self.mongodb_service.get_analytics_data_sync()
                analytics['source'] = 'mongodb'
                return analytics
            except Exception as e:
                print(f"⚠️ MongoDB analytics failed: {e}")
                self.mongodb_available = False
        
        # Use local database analytics
        local_analytics = self.local_service.get_analytics_data()
        local_analytics['source'] = 'local_database'
        return local_analytics
    
    def delete_video(self, video_id: str) -> bool:
        """Delete video from both databases"""
        local_deleted = self.local_service.delete_video(video_id)
        
        mongodb_deleted = False
        if self.mongodb_available and self.mongodb_service:
            try:
                mongodb_deleted = self.mongodb_service.delete_video(video_id)
            except Exception as e:
                print(f"⚠️ MongoDB delete failed: {e}")
        
        return local_deleted or mongodb_deleted
    
    def increment_views(self, video_id: str, ip_address: str = None) -> bool:
        """Increment view count in both databases"""
        local_success = self.local_service.increment_views(video_id, ip_address)
        
        mongodb_success = False
        if self.mongodb_available and self.mongodb_service:
            try:
                mongodb_success = self.mongodb_service.increment_views(video_id, ip_address)
            except Exception as e:
                print(f"⚠️ MongoDB increment_views failed: {e}")
        
        return local_success or mongodb_success
    
    def get_database_status(self) -> Dict[str, Any]:
        """Get status of both databases"""
        mongodb_status = {}
        if self.mongodb_service:
            mongodb_status = self.mongodb_service.get_database_status()
        
        return {
            'mongodb_available': self.mongodb_available,
            'mongodb_status': mongodb_status,
            'local_available': True,  # Local SQLite is always available
            'primary_database': 'mongodb' if self.mongodb_available else 'local',
            'mongodb_service': self.mongodb_service is not None,
            'local_service': self.local_service is not None
        }
    
    def is_connected(self) -> bool:
        """Check if any database is connected"""
        return self.mongodb_available or True  # Local is always available
