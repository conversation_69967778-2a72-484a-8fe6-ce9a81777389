import os
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import boto3
from botocore.exceptions import ClientError
from botocore.config import Config
import hashlib
import uuid

class R2Service:
    def __init__(self, r2_client):
        self.client = r2_client
        self.bucket_name = os.getenv('R2_BUCKET_NAME')
        self.custom_domain = os.getenv('R2_CUSTOM_DOMAIN')
        self.use_direct_streaming = os.getenv('USE_DIRECT_STREAMING', 'true').lower() == 'true'
    
    def upload_video_sync(self, file_data: bytes, filename: str, content_type: str = 'video/mp4') -> Optional[str]:
        """Upload video to R2 storage (synchronous)"""
        try:
            # Generate unique key for the video
            file_hash = hashlib.sha256(file_data).hexdigest()[:16]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_extension = filename.split('.')[-1] if '.' in filename else 'mp4'
            r2_key = f"videos/{timestamp}_{file_hash}.{file_extension}"

            # Upload to R2
            self.client.put_object(
                Bucket=self.bucket_name,
                Key=r2_key,
                Body=file_data,
                ContentType=content_type,
                Metadata={
                    'original_filename': filename,
                    'upload_timestamp': timestamp,
                    'file_hash': file_hash
                }
            )

            print(f"✅ R2 upload successful: {r2_key}")
            return r2_key

        except ClientError as e:
            print(f"❌ Error uploading to R2: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error uploading to R2: {e}")
            return None

    async def upload_video(self, file_data: bytes, filename: str, content_type: str = 'video/mp4') -> Optional[str]:
        """Upload video to R2 storage"""
        try:
            # Generate unique key for the video
            file_hash = hashlib.sha256(file_data).hexdigest()[:16]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_extension = filename.split('.')[-1] if '.' in filename else 'mp4'
            r2_key = f"videos/{timestamp}_{file_hash}.{file_extension}"
            
            # Upload to R2
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.client.put_object(
                    Bucket=self.bucket_name,
                    Key=r2_key,
                    Body=file_data,
                    ContentType=content_type,
                    Metadata={
                        'original_filename': filename,
                        'upload_timestamp': timestamp,
                        'file_hash': file_hash
                    }
                )
            )
            
            return r2_key
            
        except ClientError as e:
            print(f"Error uploading to R2: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error uploading to R2: {e}")
            return None
    
    async def delete_video(self, r2_key: str) -> bool:
        """Delete video from R2 storage"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.client.delete_object(
                    Bucket=self.bucket_name,
                    Key=r2_key
                )
            )
            return True
            
        except ClientError as e:
            print(f"Error deleting from R2: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error deleting from R2: {e}")
            return False
    
    async def generate_signed_url(self, r2_key: str, expiration: int = 3600) -> Optional[str]:
        """Generate signed URL for video access"""
        try:
            loop = asyncio.get_event_loop()
            signed_url = await loop.run_in_executor(
                None,
                lambda: self.client.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': self.bucket_name, 'Key': r2_key},
                    ExpiresIn=expiration
                )
            )
            
            # Replace with custom domain if configured
            if self.custom_domain and signed_url:
                # Extract the path from the signed URL
                from urllib.parse import urlparse, parse_qs
                parsed = urlparse(signed_url)
                query_params = parse_qs(parsed.query)
                
                # Construct custom domain URL
                custom_url = f"{self.custom_domain.rstrip('/')}/{r2_key}"
                
                # Add query parameters for authentication
                if query_params:
                    query_string = '&'.join([f"{k}={v[0]}" for k, v in query_params.items()])
                    custom_url += f"?{query_string}"
                
                return custom_url
            
            return signed_url
            
        except ClientError as e:
            print(f"Error generating signed URL: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error generating signed URL: {e}")
            return None
    
    async def get_video_stream_url(self, r2_key: str, session_id: str = None) -> Optional[str]:
        """Get video streaming URL based on configuration"""
        if self.use_direct_streaming:
            # Use signed URL for direct streaming (cost-optimized)
            return await self.generate_signed_url(r2_key, expiration=1800)  # 30 minutes
        else:
            # Use server proxy streaming (more secure but higher costs)
            return f"/api/videos/stream/{r2_key}?session={session_id}" if session_id else f"/api/videos/stream/{r2_key}"
    
    async def get_video_metadata(self, r2_key: str) -> Optional[Dict[str, Any]]:
        """Get video metadata from R2"""
        try:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.head_object(
                    Bucket=self.bucket_name,
                    Key=r2_key
                )
            )
            
            return {
                'content_length': response.get('ContentLength', 0),
                'content_type': response.get('ContentType', 'video/mp4'),
                'last_modified': response.get('LastModified'),
                'metadata': response.get('Metadata', {})
            }
            
        except ClientError as e:
            print(f"Error getting video metadata: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error getting video metadata: {e}")
            return None
    
    async def stream_video_chunk(self, r2_key: str, range_header: str = None) -> Optional[Dict[str, Any]]:
        """Stream video chunk for server-side streaming"""
        try:
            params = {
                'Bucket': self.bucket_name,
                'Key': r2_key
            }
            
            # Handle range requests for video seeking
            if range_header:
                params['Range'] = range_header
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.get_object(**params)
            )
            
            return {
                'body': response['Body'].read(),
                'content_length': response.get('ContentLength', 0),
                'content_range': response.get('ContentRange'),
                'content_type': response.get('ContentType', 'video/mp4'),
                'accept_ranges': response.get('AcceptRanges', 'bytes')
            }
            
        except ClientError as e:
            print(f"Error streaming video chunk: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error streaming video chunk: {e}")
            return None
    
    async def check_video_exists(self, r2_key: str) -> bool:
        """Check if video exists in R2 storage"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.client.head_object(
                    Bucket=self.bucket_name,
                    Key=r2_key
                )
            )
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            print(f"Error checking video existence: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error checking video existence: {e}")
            return False
    
    def get_storage_stats_sync(self) -> Dict[str, Any]:
        """Get storage statistics for admin dashboard (synchronous)"""
        try:
            # List all objects in the videos folder
            response = self.client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix='videos/'
            )

            total_files = 0
            total_size = 0

            if 'Contents' in response:
                for obj in response['Contents']:
                    total_files += 1
                    total_size += obj.get('Size', 0)

            print(f"✅ Storage stats: {total_files} files, {total_size / (1024*1024):.2f} MB")

            return {
                'total_files': total_files,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'total_size_gb': round(total_size / (1024 * 1024 * 1024), 2)
            }

        except ClientError as e:
            print(f"❌ Error getting storage stats: {e}")
            return {
                'total_files': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'total_size_gb': 0
            }
        except Exception as e:
            print(f"❌ Unexpected error getting storage stats: {e}")
            return {
                'total_files': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'total_size_gb': 0
            }

    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics for admin dashboard"""
        try:
            loop = asyncio.get_event_loop()
            
            # List all objects in the videos folder
            response = await loop.run_in_executor(
                None,
                lambda: self.client.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix='videos/'
                )
            )
            
            total_files = 0
            total_size = 0
            
            if 'Contents' in response:
                for obj in response['Contents']:
                    total_files += 1
                    total_size += obj.get('Size', 0)
            
            return {
                'total_files': total_files,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'total_size_gb': round(total_size / (1024 * 1024 * 1024), 2)
            }
            
        except ClientError as e:
            print(f"Error getting storage stats: {e}")
            return {
                'total_files': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'total_size_gb': 0
            }
        except Exception as e:
            print(f"Unexpected error getting storage stats: {e}")
            return {
                'total_files': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'total_size_gb': 0
            }
    
    def get_custom_domain_url(self, r2_key: str) -> str:
        """Get custom domain URL for video"""
        if self.custom_domain:
            return f"{self.custom_domain.rstrip('/')}/{r2_key}"
        else:
            # Fallback to R2 public URL format
            account_id = os.getenv('R2_ENDPOINT_URL', '').split('//')[1].split('.')[0]
            return f"https://pub-{account_id}.r2.dev/{r2_key}"
