"""
Short URL Service for Clean Video Links
"""

import string
import random
import hashlib
from typing import Optional

class ShortURLService:
    def __init__(self):
        # Characters for short URLs (alphanumeric, case-sensitive)
        self.chars = string.ascii_letters + string.digits
        self.short_url_length = 6  # Length of short URLs (abc123)
    
    def generate_short_url(self, video_id: str, title: str = "") -> str:
        """
        Generate a short URL for a video
        Uses a combination of hash and random to ensure uniqueness
        """
        # Create a hash from video_id and title for consistency
        hash_input = f"{video_id}_{title}".encode('utf-8')
        hash_digest = hashlib.md5(hash_input).hexdigest()
        
        # Use first 4 characters from hash + 2 random characters
        hash_part = ''.join(c for c in hash_digest[:8] if c in self.chars)[:4]
        
        # Add random characters to reach desired length
        remaining_length = self.short_url_length - len(hash_part)
        random_part = ''.join(random.choices(self.chars, k=remaining_length))
        
        short_url = hash_part + random_part
        
        # Ensure it's exactly the right length
        if len(short_url) > self.short_url_length:
            short_url = short_url[:self.short_url_length]
        elif len(short_url) < self.short_url_length:
            short_url += ''.join(random.choices(self.chars, k=self.short_url_length - len(short_url)))
        
        return short_url
    
    def generate_readable_short_url(self, title: str) -> str:
        """
        Generate a more readable short URL based on title
        Example: "Anime Episode 1" -> "anime1" + random chars
        """
        if not title:
            return self.generate_random_short_url()
        
        # Clean title: remove special chars, convert to lowercase
        clean_title = ''.join(c.lower() for c in title if c.isalnum())
        
        # Take first few characters from title
        title_part = clean_title[:4] if len(clean_title) >= 4 else clean_title
        
        # Add random characters to reach desired length
        remaining_length = self.short_url_length - len(title_part)
        if remaining_length > 0:
            random_part = ''.join(random.choices(self.chars, k=remaining_length))
            short_url = title_part + random_part
        else:
            short_url = title_part[:self.short_url_length]
        
        return short_url
    
    def generate_random_short_url(self) -> str:
        """Generate a completely random short URL"""
        return ''.join(random.choices(self.chars, k=self.short_url_length))
    
    def is_valid_short_url(self, short_url: str) -> bool:
        """Check if a short URL is valid format"""
        if not short_url:
            return False
        
        if len(short_url) != self.short_url_length:
            return False
        
        # Check if all characters are valid
        return all(c in self.chars for c in short_url)
    
    def generate_unique_short_url(self, video_id: str, title: str, existing_urls: set) -> str:
        """
        Generate a unique short URL that doesn't conflict with existing ones
        """
        max_attempts = 100
        
        for attempt in range(max_attempts):
            if attempt == 0:
                # First try: use title-based generation
                short_url = self.generate_readable_short_url(title)
            elif attempt == 1:
                # Second try: use hash-based generation
                short_url = self.generate_short_url(video_id, title)
            else:
                # Subsequent tries: random generation
                short_url = self.generate_random_short_url()
            
            if short_url not in existing_urls:
                return short_url
        
        # Fallback: use timestamp-based generation
        import time
        timestamp = str(int(time.time()))[-4:]
        random_part = ''.join(random.choices(self.chars, k=2))
        return timestamp + random_part

# Global instance
short_url_service = ShortURLService()
