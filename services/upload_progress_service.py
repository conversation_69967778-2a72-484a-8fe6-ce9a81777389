"""
Upload Progress Service with Real-time WebSocket Updates
"""

import uuid
import time
import threading
from typing import Dict, Any, Optional
from flask_socketio import Socket<PERSON>, emit
from services import service_registry

class UploadProgressService:
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.active_uploads: Dict[str, Dict[str, Any]] = {}
        self.upload_lock = threading.Lock()
    
    def create_upload_session(self, filename: str, file_size: int, title: str) -> str:
        """Create a new upload session and return session ID"""
        session_id = str(uuid.uuid4())
        
        with self.upload_lock:
            self.active_uploads[session_id] = {
                'filename': filename,
                'file_size': file_size,
                'title': title,
                'uploaded_bytes': 0,
                'progress_percent': 0,
                'status': 'initializing',
                'start_time': time.time(),
                'current_step': 'Preparing upload...',
                'error': None,
                'video_id': None
            }
        
        return session_id
    
    def update_progress(self, session_id: str, uploaded_bytes: int, status: str = None, current_step: str = None):
        """Update upload progress and emit to client"""
        with self.upload_lock:
            if session_id not in self.active_uploads:
                return

            upload_info = self.active_uploads[session_id]
            upload_info['uploaded_bytes'] = uploaded_bytes
            upload_info['progress_percent'] = min(100, (uploaded_bytes / upload_info['file_size']) * 100)

            if status:
                upload_info['status'] = status
            if current_step:
                upload_info['current_step'] = current_step

            # Calculate upload speed and time estimates
            elapsed_time = time.time() - upload_info['start_time']
            if elapsed_time > 0 and uploaded_bytes > 0:
                # Speed in MB/s
                upload_info['speed_mbps'] = (uploaded_bytes / (1024 * 1024)) / elapsed_time

                # Speed in bytes/s for ETA calculation
                speed_bps = uploaded_bytes / elapsed_time

                # Calculate ETA
                remaining_bytes = upload_info['file_size'] - uploaded_bytes
                if speed_bps > 0 and remaining_bytes > 0:
                    eta_seconds = remaining_bytes / speed_bps
                    upload_info['eta_seconds'] = eta_seconds
                else:
                    upload_info['eta_seconds'] = 0
            else:
                upload_info['speed_mbps'] = 0
                upload_info['eta_seconds'] = 0

            # Format file sizes
            uploaded_mb = uploaded_bytes / (1024 * 1024)
            total_mb = upload_info['file_size'] / (1024 * 1024)

            # Emit progress update
            self.socketio.emit('upload_progress', {
                'session_id': session_id,
                'progress_percent': upload_info['progress_percent'],
                'uploaded_bytes': uploaded_bytes,
                'total_bytes': upload_info['file_size'],
                'uploaded_mb': uploaded_mb,
                'total_mb': total_mb,
                'status': upload_info['status'],
                'current_step': upload_info['current_step'],
                'speed_mbps': upload_info.get('speed_mbps', 0),
                'eta_seconds': upload_info.get('eta_seconds', 0),
                'elapsed_time': elapsed_time
            })
    
    def set_error(self, session_id: str, error_message: str):
        """Set upload error status"""
        with self.upload_lock:
            if session_id not in self.active_uploads:
                return
            
            self.active_uploads[session_id]['status'] = 'error'
            self.active_uploads[session_id]['error'] = error_message
            
            self.socketio.emit('upload_error', {
                'session_id': session_id,
                'error': error_message
            })
    
    def set_complete(self, session_id: str, video_id: str):
        """Mark upload as complete"""
        with self.upload_lock:
            if session_id not in self.active_uploads:
                return
            
            upload_info = self.active_uploads[session_id]
            upload_info['status'] = 'complete'
            upload_info['video_id'] = video_id
            upload_info['progress_percent'] = 100
            upload_info['current_step'] = 'Upload completed successfully!'
            
            self.socketio.emit('upload_complete', {
                'session_id': session_id,
                'video_id': video_id,
                'message': 'Video uploaded successfully!'
            })
    
    def get_upload_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get upload information"""
        with self.upload_lock:
            return self.active_uploads.get(session_id)
    
    def cleanup_session(self, session_id: str):
        """Clean up completed upload session"""
        with self.upload_lock:
            if session_id in self.active_uploads:
                del self.active_uploads[session_id]
    
    def upload_video_with_progress(self, session_id: str, file_data: bytes, filename: str, content_type: str, title: str, description: str):
        """Upload video with real-time progress updates"""
        try:
            # Get services
            firebase_service = service_registry.get_firebase_service()
            r2_service = service_registry.get_r2_service()
            
            if not firebase_service or not r2_service:
                self.set_error(session_id, "Services unavailable")
                return None
            
            # Step 1: Prepare upload
            self.update_progress(session_id, 0, 'uploading', 'Preparing file upload...')
            time.sleep(0.5)  # Small delay for UI feedback
            
            # Step 2: Upload to R2 with progress simulation
            self.update_progress(session_id, len(file_data) * 0.1, 'uploading', 'Uploading to cloud storage...')
            
            r2_key = r2_service.upload_video_sync(file_data, filename, content_type)
            if not r2_key:
                self.set_error(session_id, "Failed to upload to cloud storage")
                return None
            
            # Step 3: Upload progress simulation
            self.update_progress(session_id, len(file_data) * 0.8, 'uploading', 'Processing video...')
            time.sleep(0.3)
            
            # Step 4: Create database record
            self.update_progress(session_id, len(file_data) * 0.9, 'uploading', 'Creating video record...')
            
            video_data = {
                'title': title,
                'description': description,
                'filename': filename,
                'file_size': len(file_data),
                'r2_key': r2_key
            }
            
            video_id = firebase_service.create_video(video_data)
            if not video_id:
                self.set_error(session_id, "Failed to create video record")
                return None
            
            # Step 5: Complete
            self.update_progress(session_id, len(file_data), 'complete', 'Upload completed successfully!')
            self.set_complete(session_id, video_id)
            
            return video_id
            
        except Exception as e:
            self.set_error(session_id, f"Upload failed: {str(e)}")
            return None

# Global instance
upload_progress_service = None

def init_upload_progress_service(socketio: SocketIO):
    """Initialize the upload progress service"""
    global upload_progress_service
    upload_progress_service = UploadProgressService(socketio)
    return upload_progress_service

def get_upload_progress_service() -> Optional[UploadProgressService]:
    """Get the upload progress service instance"""
    return upload_progress_service
